import swaggerJsDoc, { Options } from "swagger-jsdoc";
import path from "path";
import dotenv from "dotenv";
dotenv.config();

const BASE_URL: string = process.env.BASE_URL || "http://localhost:5000";

const options: Options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Findanyagent API",
      version: "1.0.0",
      description: "API documentation for your Express project",
    },
    servers: [
      {
        url: "http://localhost:5000/api/v1",
        description: "Local development server",
      },
      {
        url: `${BASE_URL}/api/v1`,
        description: "Production server",
      },
    ],
  },
  apis: [
    path.resolve(__dirname, "../routes/**/*.ts"),
    path.resolve(__dirname, "../docs/swagger/**/*.yaml"),
  ],
};

const swaggerSpec = swaggerJsDoc(options);

export default swaggerSpec;
