import { Request, Response } from "express";
import { AUTH } from "../../../utils/database/queries/auth";
import { db } from "../../../config/database";
import {
  errorCatchResponse,
  errorResponse,
  responseData,
} from "../../../utils/response";
import asyncHandler from "../../../middleware/trycatch";
import { AGENTS } from "../../../utils/database/queries/agentDetails";
import { TABLE } from "../../../utils/database/table";
import { sendAccountStatusUpdatedEmail } from "../../../utils/services/nodemailer/sendAccountStatusUpdatedEmail";
import { AGENT_ACCOUNT_TYPE } from "../../../utils/enums/account.enum";

export const getAgentAccounts = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { status, accountType } = req.query;

      let accountTypes = [];

      if (accountType == "All") {
        accountTypes = [
          AGENT_ACCOUNT_TYPE.INDIVIDUAL,
          AGENT_ACCOUNT_TYPE.COMPANY_OR_AGENCY,
        ];
      } else if (accountType == "Agents") {
        accountTypes = [AGENT_ACCOUNT_TYPE.INDIVIDUAL];
      } else {
        accountTypes = [AGENT_ACCOUNT_TYPE.COMPANY_OR_AGENCY];
      }

      // Convert to string[]
      const statusNames: string[] = Array.isArray(status)
        ? status.filter((s): s is string => typeof s === "string") // filters out undefined and ParsedQs
        : typeof status === "string"
        ? [status]
        : [];

      const selectedStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      let rows,
        statusId = null;

      if (selectedStatus.rows.length > 0) {
        statusId = selectedStatus.rows[0].id;
        const results = await db.query(AGENTS.GET_AGENT_DETAILS_WITH_STATUS, [
          statusId,
          accountTypes,
        ]);
        rows = results.rows;
      } else {
        const results = await db.query(AGENTS.GET_AGENT_DETAILS, [
          accountTypes,
        ]);
        rows = results.rows;
      }

      if (!rows.length) {
        return errorResponse(res, "No record found");
      }

      const { rows: profileCounts } = await db.query(
        AGENTS.GET_PROFILES_COUNT_STATUS_WISE,
        [accountTypes, statusId ? statusId : null]
      );

      return responseData(res, 200, "Data retrieved successfully", {
        profiles: rows,
        profileCounts,
      });
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getAgentAccountsById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const { rows } = await db.query(AUTH.SELECT_BY_ID, [id]);

      if (!rows.length) {
        return errorResponse(res, "No record found");
      }

      if (rows[0].accountType === "Individual") {
        const individualAgentData = await db.query(
          AGENTS.GET_INDIVIDUAL_AGENYT_DETAILS_BY_ID,
          [id]
        );

        if (!individualAgentData.rows.length) {
          return errorResponse(res, "No record found");
        }

        const individualAgent = individualAgentData.rows[0];

        // Append base URL to files
        // individualAgent.profilePhotos = appendBaseUrlToFiles(individualAgent.profilePhotos);
        // individualAgent.emiratesId = appendBaseUrlToFiles(individualAgent.emiratesId);
        // individualAgent.passport = appendBaseUrlToFiles(individualAgent.passport);
        // individualAgent.employmentProof = appendBaseUrlToFiles(individualAgent.employmentProof);
        // individualAgent.freelancePermitDocs = appendBaseUrlToFiles(individualAgent.freelancePermitDocs);
        // individualAgent.licenseDoc = appendBaseUrlToFiles(individualAgent.licenseDoc);
        // individualAgent.licenseDocs = appendBaseUrlToFiles(individualAgent.licenseDocs);
        // individualAgent.visaDoc = appendBaseUrlToFiles(individualAgent.visaDoc);
        // individualAgent.visa = appendBaseUrlToFiles(individualAgent.visa);

        delete individualAgent.passwordHash;
        const agentlicenses = await db.query(
          `SELECT * FROM agentlicenses WHERE "agentId" = $1 OR "agencyId" = $1`,
          [id]
        );
        const companyrole = await db.query(
          `SELECT * FROM companyrole  WHERE "companyId" = $1`,
          [id]
        );

        return responseData(res, 200, "Data retrieved successfully", {
          ...individualAgent,
          agentlicenses: agentlicenses.rows,
          companyrole: companyrole.rows,
        });
      } else if (rows[0].accountType === "Company/Agency/PropertyDeveloper") {
        const companyAgentData = await db.query(
          AGENTS.GET_COMPANY_AGENT_DETAILS_BY_ID,
          [id]
        );

        if (!companyAgentData.rows.length) {
          return errorResponse(res, "No record found");
        }

        const companyAgent = companyAgentData.rows[0];

        const operationArea = await db.query(
          `SELECT * FROM list.countries WHERE "id" = ANY($1)`,
          [companyAgent.operationArea.split(",")]
        );
        const nationality = await db.query(
          `SELECT * FROM list.countries WHERE "id" = $1`,
          [companyAgent.nationality]
        );

        companyAgent.operationArea =
          operationArea?.rows?.map((area) => area.name) || [];
        companyAgent.nationality = nationality?.rows?.[0]?.name || "";

        delete companyAgent.passwordHash;
        const agentlicenses = await db.query(
          `SELECT * FROM agentlicenses WHERE "agentId" = $1 OR "agencyId" = $1`,
          [id]
        );
        const companyrole = await db.query(
          `SELECT * FROM companyrole  WHERE "companyId" = $1`,
          [id]
        );

        return responseData(res, 200, "Data retrieved successfully", {
          ...companyAgent,
          agentlicenses: agentlicenses.rows,
          companyrole: companyrole.rows,
        });
      }
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const updateProfileStatusById = asyncHandler(
  async (req: Request, res: Response) => {
    const { profileId } = req.params;
    const { status, rejectionReason, requiredFields, isPrivate, licenseTag } =
      req.body;

    let { isLicensed: rawIsLicensed = false } = req.body;

    const isLicensed =
      typeof rawIsLicensed === "string"
        ? rawIsLicensed.trim().toLowerCase() === "true"
        : Boolean(rawIsLicensed);

    if (!profileId) {
      return errorResponse(res, "Missing profileId or status.");
    }

    const client = await db.connect();

    try {
      let normalizedIsPrivate = false;
      if (typeof isPrivate === "string") {
        normalizedIsPrivate = isPrivate.toLowerCase() === "true";
      } else if (typeof isPrivate === "boolean") {
        normalizedIsPrivate = isPrivate;
      }

      await client.query("BEGIN");

      const { rows: currentProfileDetails } = await client.query(
        AUTH.SELECT_BY_ID,
        [profileId]
      );

      const oldStatusId = currentProfileDetails[0].statusId;

      let newStatusId = oldStatusId;

      // Step 2: Fetch the new statusId from the status table
      if (status) {
        const statusNames = [status];
        const pendingStatus = await client.query(
          AUTH.SELECT_ACCOUNT_STATUS(statusNames),
          statusNames
        );

        if (pendingStatus.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Status not found.");
        }

        newStatusId = pendingStatus.rows[0].id;
      }

      const updateQuery = `
        UPDATE ${TABLE.PROFILE_TABLE}
        SET "statusId" = $1,
        "rejectionReason" = $2,
        "requiredFields" = $3,
        "isLicensed" = $4,
        "licenseTag" = $5
        WHERE id = $6
        RETURNING *;
      `;

      const result = await client.query(updateQuery, [
        newStatusId,
        rejectionReason || null,
        requiredFields || null,
        isLicensed,
        licenseTag,
        profileId,
      ]);

      if (result.rows.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile not found.");
      }

      const { rows } = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      if (!rows.length) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile not found.");
      }

      const user = rows[0];

      if (
        (status && status.trim().toLowerCase() != "activated") ||
        isPrivate.toLowerCase() === "true"
      ) {
        const insertReasonQuery = `
        INSERT INTO prf.reasons (
          "profileId",
          "reason",
          "createdBy",
          "oldStatusId",
          "newStatusId",
          "isPrivate"
        )
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *;
      `;

        const userId = req.user.id;

        await client.query(insertReasonQuery, [
          profileId,
          rejectionReason || null,
          userId,
          oldStatusId,
          newStatusId,
          normalizedIsPrivate,
        ]);
      }

      const fullName = `${user.firstName} ${user.lastName}`;
      const email = user.email;

      if (
        status &&
        (status.trim().toLowerCase() == "activated" ||
          status.trim().toLowerCase() == "rejected" ||
          status.trim().toLowerCase() == "incomplete")
      ) {
        await sendAccountStatusUpdatedEmail(fullName, email, status, res);
      }

      await client.query("COMMIT");

      return responseData(res, 200, "Status updated successfully.", user);
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error updating statusId:", error);
      res.status(500).json({ message: "Server error updating statusId." });
    } finally {
      client.release();
    }
  }
);

export const getAllVerifiedAgentAccounts = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { status, accountType } = req.query;

      let accountTypes = [];

      if (accountType == "All") {
        accountTypes = [
          AGENT_ACCOUNT_TYPE.INDIVIDUAL,
          AGENT_ACCOUNT_TYPE.COMPANY_OR_AGENCY,
        ];
      } else if (accountType == "Agents") {
        accountTypes = [AGENT_ACCOUNT_TYPE.INDIVIDUAL];
      } else {
        accountTypes = [AGENT_ACCOUNT_TYPE.COMPANY_OR_AGENCY];
      }

      // Convert to string[]
      const statusNames: string[] = Array.isArray(status)
        ? status.filter((s): s is string => typeof s === "string") // filters out undefined and ParsedQs
        : typeof status === "string"
        ? [status]
        : [];

      const selectedStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      let rows;

      if (selectedStatus.rows.length > 0) {
        const statusId = selectedStatus.rows[0].id;
        const results = await db.query(
          AGENTS.GET_APPROVED_AGENT_DETAILS_WITH_STATUS,
          [statusId, accountTypes]
        );
        rows = results.rows;
      } else {
        const results = await db.query(AGENTS.GET_APPROVED_AGENT_DETAILS, [
          accountTypes,
        ]);
        rows = results.rows;
      }

      if (!rows.length) {
        return errorResponse(res, "No record found");
      }

      return responseData(res, 200, "Data retrieved successfully", rows);
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getApplicatinReasons = asyncHandler(
  async (req: Request, res: Response) => {
    const { profileId } = req.params;

    if (!profileId) {
      return errorResponse(res, "Missing Profile ID.");
    }

    try {
      const getReasonsQuery = `
      SELECT 
        r.id,
        r."profileId",
        r.reason,
        r."isPrivate",
        r."createdBy",
        r."created_at",
        u."firstName",
        u."lastName",
        u.email,
        old_status."name" AS "oldStatusName",
        new_status."name" AS "newStatusName"
      FROM prf.reasons r
      INNER JOIN ${TABLE.PROFILE_TABLE} u ON r."createdBy" = u.id
      LEFT JOIN ${TABLE.STATUS} old_status ON r."oldStatusId" = old_status.id
      LEFT JOIN ${TABLE.STATUS} new_status ON r."newStatusId" = new_status.id
      WHERE r."profileId" = $1
      ORDER BY r."created_at" DESC;
    `;

      const { rows: reasons } = await db.query(getReasonsQuery, [profileId]);

      return responseData(res, 200, "Reasons retrieved successfully", {
        reasons,
      });
    } catch (error) {
      console.error("Error fetching document reasons:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);
