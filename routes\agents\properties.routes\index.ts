import { Router } from "express";
import {
  createOrUpdateProperty,
  deleteProperty,
  getAllProperties,
  getPropertyById,
  togglePropertyFlag,
  updatePropertyPhotos,
  updatePropertyStatus,
} from "../../../controller/agents/properties.controller";
import { storageData } from "../../../utils/services/multer";

const router = Router();
const upload = storageData("documents");

const fields = [
  { name: "govtIssuedQr", maxCount: 1 },
  { name: "propertyPhotos", maxCount: 10 },
];

// GET all properties with pagination and filters
router.get("/", getAllProperties);

// GET property by ID
router.get("/:id", getPropertyById);

// POST create or update a property
router.post("/", upload.fields(fields), createOrUpdateProperty);

// PUT update property status
router.put("/:id/status", upload.none(), updatePropertyStatus);

// PUT toggle featured/verified flags
router.put("/:id/flag", upload.none(), togglePropertyFlag);

// DELETE property
router.delete("/:id", deleteProperty);

// Upload property photos
router.patch(
  "/:propertyId/photos",
  upload.fields([{ name: "propertyPhotos", maxCount: 10 }]),
  updatePropertyPhotos
);

export default router;
