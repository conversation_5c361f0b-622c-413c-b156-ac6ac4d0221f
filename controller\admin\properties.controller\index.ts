import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import { TABLE } from "../../../utils/database/table";

// -------------------- GET ALL PROPERTIES --------------------
export const getAllProperties = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const {
        page = 1,
        pageSize = 10,
        status,
        propertyTypeId,
        locationId,
        listingType,
      } = req.query;

      const limit = parseInt(String(pageSize), 10) || 10;
      const currentPage = parseInt(String(page), 10) || 1;

      let statusId: number | null = null;

      if (status && String(status).trim().toLowerCase() !== "all") {
        const statusName = String(status).trim();
        const { rows: statusRow } = await db.query(
          `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
          [statusName]
        );
        if (statusRow.length === 0)
          return error(res, 400, `Status '${statusName}' not found.`);
        statusId = statusRow[0].id;
      }

      const params = [
        statusId,
        propertyTypeId ? Number(propertyTypeId) : null,
        locationId ? Number(locationId) : null,
        listingType ? Number(listingType) : null,
        limit,
        currentPage,
      ];

      const { rows } = await db.query(
        `SELECT * FROM get_filtered_properties($1, $2, $3, $4, $5, $6)`,
        params
      );

      const statusCountParams = [
        propertyTypeId ? Number(propertyTypeId) : null,
        locationId ? Number(locationId) : null,
        listingType ? Number(listingType) : null,
      ];

      const { rows: statusCounts } = await db.query(
        `SELECT * FROM get_property_status_counts($1, $2, $3)`,
        statusCountParams
      );

      if (!rows.length) {
        return responseData(res, 200, "No properties found", {
          properties: [],
          pagination: {
            total: 0,
            totalPages: 0,
            currentPage,
            perPage: limit,
          },
          statusCounts,
        });
      }

      const { total_count, total_pages } = rows[0];
      const properties = rows.map(
        ({ total_count, total_pages, ...rest }) => rest
      );

      return responseData(res, 200, "Properties fetched successfully", {
        properties,
        pagination: {
          total: total_count,
          totalPages: total_pages,
          currentPage,
          perPage: limit,
        },
        statusCounts,
      });
    } catch (error) {
      console.error(error);
      return response(res, 500, "Something went wrong. Please try again later");
    }
  }
);

// -------------------- GET PROPERTY BY ID --------------------
export const getPropertyById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const id = Number(req.params.id);
      if (!id) return error(res, 400, "Property ID is required");

      const { rows } = await db.query("SELECT * FROM get_property_detail($1)", [
        id,
      ]);

      if (rows.length === 0) return error(res, 404, "Property not found");

      return responseData(res, 200, "Property fetched successfully", rows[0]);
    } catch (error) {
      console.log(error);
      return response(
        res,
        500,
        "Something wents wrong. Please try again later"
      );
    }
  }
);

// -------------------- UPDATE STATUS --------------------
export const updatePropertyStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { status } = req.body;

    if (!id || !status) {
      return error(res, 400, "Property ID and status are required");
    }

    const statusName = String(status).trim();

    // Lookup statusId from name
    const { rows: statusRow } = await db.query(
      `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
      [statusName]
    );

    if (statusRow.length === 0) {
      return error(res, 400, `Status '${statusName}' not found.`);
    }

    const statusId = statusRow[0].id;

    // Direct update query
    const result = await db.query(
      `UPDATE agn.properties SET "statusId" = $1, "modifiedOn" = NOW() WHERE id = $2`,
      [statusId, id]
    );

    if (result.rowCount === 0) {
      return error(res, 404, "Property not found or update failed");
    }

    return response(res, 200, "Status updated successfully");
  }
);

// -------------------- TOGGLE PROPERTY FLAG --------------------
export const togglePropertyFlag = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { column } = req.body;

    if (!id || !column)
      return error(res, 400, "Property ID and column name are required");

    const allowedColumns = ["isFeatured", "isVerified"];
    if (!allowedColumns.includes(column))
      return error(res, 400, "Invalid column name");

    // Step 1: Get current value
    const result = await db.query(
      `SELECT "${column}" FROM agn.properties WHERE id = $1`,
      [id]
    );

    if (result.rowCount === 0) {
      return error(res, 404, "Property not found");
    }

    const currentValue = result.rows[0][column];
    const newValue = !currentValue;

    // Step 2: Update with toggled value
    const updateResult = await db.query(
      `
        UPDATE agn.properties
        SET "${column}" = $1, "modifiedOn" = NOW()
        WHERE id = $2
      `,
      [newValue, id]
    );

    return response(
      res,
      200,
      `The property’s '${
        column === "isFeatured" ? "Featured" : "Verified"
      }' status has been successfully toggled to '${
        newValue ? "Enabled" : "Disabled"
      }'.`
    );
  }
);

// -------------------- DELETE PROPERTY --------------------
export const deleteProperty = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Property ID is required");

    // Ensure the property exists before deletion
    const { rowCount } = await db.query(
      `DELETE FROM agn.properties WHERE id = $1`,
      [id]
    );

    if (rowCount === 0) {
      return error(res, 404, "Property not found or already deleted");
    }

    return response(res, 200, "Property deleted successfully.");
  }
);

// -------------------- CREATE NOTE FOR PROPERTY --------------------
export const createNote = asyncHandler(async (req: Request, res: Response) => {
  /* ---------- validate input ---------- */
  const propertyId = Number(req.params.id); // make sure it is a number
  const { note } = req.body;

  if (!note || note.trim().length === 0) {
    return error(res, 400, "Note cannot be empty.");
  }

  /* ---------- build the exact 21-arg list ---------- */
  const params = [
    10, // p_fnid
    null, // p_lead_id
    null, // p_full_name
    null, // p_email
    null, // p_phone
    null, // p_license_number
    null, // p_company
    null, // p_lead_type
    null, // p_status_id
    null, // p_source
    null, // p_is_converted
    null, // p_note_id
    "property", // p_entity_type
    propertyId, // p_entity_id
    note, // p_note
    null, // p_sortby
    null, // p_filter_column
    null, // p_filter_value
    1, // p_page_no
    10, // p_page_size
    null, // p_search_text
  ];

  /* ---------- make the call ---------- */
  const client = await db.connect();
  try {
    // $1,$2,…,$21  – generated from params.length
    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes;

    if (result.type === "error") {
      return error(res, 400, result.message);
    }

    return responseData(res, 201, "Note added successfully.", result.data);
  } catch (err) {
    console.error("Create note failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

export const getNotesOfProperty = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    if (!id) return error(res, 400, "Property ID is required.");

    const client = await db.connect();

    try {
      await client.query("BEGIN");

      // Step 1: Get statusId for "Lost"
      const notes = await client.query(
        `SELECT * FROM ${TABLE.NOTES}  WHERE  "entityId" = $1 AND "entityType" = 'property' ORDER BY "created_at" DESC`,
        [id]
      );

      await client.query("COMMIT");
      return responseData(res, 200, "Notes Fetched.", notes.rows);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Notes  Fetched failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);
