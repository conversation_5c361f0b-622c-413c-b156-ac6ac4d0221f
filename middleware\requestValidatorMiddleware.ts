import { Request, Response, NextFunction } from 'express';
import { ZodObject } from 'zod';
import { RequestHandler } from 'express';
import { errorResponse } from '../utils/response';

export const requestValidatorMiddleware = (schema: ZodObject<any>): RequestHandler => {
  return  (req: Request, res: Response, next: NextFunction) => {
    const result = schema.safeParse(req.body);
    if (!result.success) {
        const messages = Object.values(result.error.flatten().fieldErrors)
                                .flat()
                                .filter(Boolean); // remove any undefined/null
        
        errorResponse(res, messages.join(', '));
        return;
    }
    req.body = result.data;
    next();
  };
};