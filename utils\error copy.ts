import { z } from "zod";

export class ValidationError extends <PERSON>rror {
  constructor(message: string) {
    super(message);
    this.name = "ValidationError";
  }
}

export class AppError extends Error {
  statusCode: number;
  constructor(message: string, statusCode = 500) {
    super(message);
    this.statusCode = statusCode;
  }
}

/** Helper to format zod errors for API responses */
export function formatZodError(err: z.ZodError) {
  return err.issues.map((i) => ({
    path: i.path.join("."),
    message: i.message,
  }));
}
