import { Request } from "express";
import {
  CreateServiceDTO,
  CreateServiceSchema,
} from "../../dto/service/ServiceDTO";
import { ServiceRepository } from "../../repo/service/ServiceRepository";
import { AppError, formatZodError } from "../../utils/error copy";
import { db } from "../../config/database";
import {
  deleteFileFromS3,
  uploadFileToS3,
} from "../../utils/services/s3-bucket";
import { AUTH } from "../../utils/database/queries/auth";
import path from "path";
import {
  ListServiceQueryDTO,
  ListServiceQuerySchema,
} from "../../dto/service/ListServiceQueryDTO";
import { normalizeToArray } from "../../dto/service/HelperDTO";
import {
  UpdateServiceDTO,
  UpdateServiceSchema,
} from "../../dto/service/UpdateServiceDTO";
import { unlinkUploadedFiles } from "../../utils/helperFunctions/unlinkUploadedFiles";
import { PoolClient } from "pg";
import { StatusRepository } from "../../repo/status/StatusRepository";

export class AgentServicesService {
  private repo = new ServiceRepository();
  private statusRepo = new StatusRepository();

  async getAllServices(rawQuery: any) {
    const parsed = ListServiceQuerySchema.safeParse(rawQuery);
    if (!parsed.success) {
      throw new AppError(
        JSON.stringify({ errors: formatZodError(parsed.error) }),
        400
      );
    }
    const q: ListServiceQueryDTO = parsed.data;

    const listData = await this.repo.list(q);
    const listCountData = await this.repo.listCounts(q);

    listData.pagination.total = listCountData.total ?? 0;

    return listData;
  }

  async getAllServicesForAdmin(rawQuery: any) {
    const parsed = ListServiceQuerySchema.safeParse(rawQuery);
    if (!parsed.success) {
      throw new AppError(
        JSON.stringify({ errors: formatZodError(parsed.error) }),
        400
      );
    }
    const q: ListServiceQueryDTO = parsed.data;

    const listData = await this.repo.adminList(q);
    const listCountData = await this.repo.adminListCounts(q);

    listData.pagination.total = listCountData.total ?? 0;

    return listData;
  }

  async getServiceById(id: number) {
    if (!Number.isFinite(id)) throw { statusCode: 400, message: "Invalid id" };
    const svc = await this.repo.getById(id);
    if (!svc) throw { statusCode: 404, message: "Service not found" };
    return svc;
  }

  async createOrUpdateService(req: Request) {
    const client: PoolClient = await db.connect();

    // Multer files
    const files = req.files as
      | { [field: string]: Express.Multer.File[] }
      | undefined;

    const photos = files?.["servicePhotos"] ?? [];

    // track what we may need to clean up
    const uploadedPhotoKeys: string[] = [];
    const localFilesToCleanup: string[] = [];

    const id = req.body.id ? Number(req.body.id) : null;

    let { isRemote, isFree, locationIds, serviceIds } = req.body;

    // Normalize string values ("true"/"false") into booleans
    if (typeof isRemote === "string") {
      isRemote = isRemote.toLowerCase() === "true";
    }
    if (typeof isFree === "string") {
      isFree = isFree.toLowerCase() === "true";
    }

    req.body.locationIds = normalizeToArray(locationIds);
    req.body.serviceIds = normalizeToArray(serviceIds);

    // Ensure they're always booleans (fallback to false if undefined/null)
    req.body.isRemote = Boolean(isRemote);
    req.body.isFree = Boolean(isFree);

    req.body.profileId = req.user?.id;
    req.body.createdBy = req.user?.id;

    // Resolve statusId = "Activated"
    const status = await this.statusRepo.getStatusDetailsByStatusName([
      "Activated",
    ]);

    if (!status) {
      throw new AppError("Status 'Activated' not found", 400);
    }

    req.body.statusId = status[0].id;

    const schema =
      id != null || id != 0 ? UpdateServiceSchema : CreateServiceSchema;
    const parsed = schema.safeParse({ id, ...req.body });
    if (!parsed.success) {
      console.log(parsed)
      if (photos.length > 0) {
        await unlinkUploadedFiles(photos);
      }

      throw new AppError(
        JSON.stringify({ errors: formatZodError(parsed.error) }),
        400
      );
    }

    console.log('object')
    try {
      const dto = parsed.data as UpdateServiceDTO | CreateServiceDTO;

      // Determine userType for S3 foldering
      const { rows: profileRows } = await db.query(AUTH.SELECT_BY_ID, [
        req.user?.id,
      ]);

      const accountType = profileRows[0]?.accountType;
      let userType: "user" | "agent" | "agency" = "user";
      if (accountType === "Individual") userType = "agent";
      else if (accountType === "Company/Agency/PropertyDeveloper")
        userType = "agency";

      // Upload photos to S3 first; collect uploaded keys for potential rollback
      if (photos.length > 0) {
        await Promise.all(
          photos.map(async (f) => {
            // remember local file to clean later (success or error)
            const localPath =
              (f as any).path || path.join("public", "documents", f.filename);
            localFilesToCleanup.push(localPath);

            const u = await uploadFileToS3(
              f.filename,
              f,
              userType,
              "service-photo"
            );
            uploadedPhotoKeys.push(u.fileKey);
            return u;
          })
        );
      }

      console.log(uploadedPhotoKeys)
      const servicePhotos = {
        photos: uploadedPhotoKeys,
        profileId: req.body.profileId!,
        statusId: req.body.statusId!,
        createdBy: req.body.createdBy!,
      };

      try {
        await client.query("BEGIN");

        let serviceId = null;

        console.log(id, typeof id);

        if (id !== null) {
          const result = await this.repo.update(
            client,
            dto as UpdateServiceDTO
          );

          serviceId = result.id;

          await this.repo.deleteExistingServiceTypes(client, serviceId);
          await this.repo.deleteExistingServiceLocations(client, serviceId);
        } else {
          const result = await this.repo.create(
            client,
            dto as CreateServiceDTO
          );

          serviceId = result.id;
        }

        if (serviceId) {
          // Create Service types by Service ID
          await this.repo.createServiceTypes(
            client,
            serviceId,
            dto as CreateServiceDTO
          );

          if (isRemote == false) {
            // Create Service locations by Service ID
            await this.repo.createServiceLocations(
              client,
              serviceId,
              dto as CreateServiceDTO
            );
          }

          // Create service photos by service ID
          await this.repo.createServicePhotos(client, serviceId, servicePhotos);
        }

        await client.query("COMMIT");
        return this.repo.getById(serviceId);
      } catch (err) {
        if (client) {
          try {
            await client.query("ROLLBACK");
          } catch {}
        }

        if (servicePhotos.photos?.length) {
          await Promise.allSettled(
            servicePhotos.photos.map((key) => deleteFileFromS3(key!))
          );
        }

        throw err;
      } finally {
        if (client) client.release();
      }
      // Persist (repo uses uploaded S3 keys)
    } catch (err) {
      // Roll back S3 uploads made in this request
      await Promise.allSettled(
        uploadedPhotoKeys.map((key) => deleteFileFromS3(key!))
      );

      if (photos && !uploadedPhotoKeys) {
        await unlinkUploadedFiles(photos);
      }

      // Re-throw the original error
      throw err;
    }
  }

  async updateStatus(id: number, status: number) {
    const statusDetails = await this.statusRepo.getStatusDetailsById(status);

    if (!status) {
      throw new AppError("Selected status not found", 400);
    }

    await this.repo.updateStatus(id, statusDetails.id);
  }

  async getStatusById(statusId: number) {
    return await this.statusRepo.getStatusDetailsById(statusId);
  }

  async deleteService(id: number) {
    if (!Number.isFinite(id)) throw { statusCode: 400, message: "Invalid id" };
    
    // Get service details before deletion for email notification
    const serviceDetails = await this.repo.getById(id);
    if (!serviceDetails) {
      throw { statusCode: 404, message: "Service not found" };
    }
    
    // Perform the deletion
    await this.repo.delete(id);
    
    // Send email notification to service owner
    try {
      const { sendServiceDeletionEmail } = await import("../../utils/services/nodemailer/sendServiceDeletionEmail");
      
      // Extract owner information from profile
      const ownerName = serviceDetails.profile?.firstName 
        ? `${serviceDetails.profile.firstName} ${serviceDetails.profile.lastName || ''}`.trim()
        : serviceDetails.profile?.firstName || 'User';
      
      const ownerEmail = serviceDetails.profile?.email;
      
      if (ownerEmail) {
        await sendServiceDeletionEmail(
          serviceDetails.title || 'Your Service',
          id,
          ownerName,
          ownerEmail
        );
      }
    } catch (emailError) {
      console.error("Failed to send service deletion email:", emailError);
      // Don't throw error to avoid disrupting the deletion flow
    }
  }

  async updatePhotos(req: Request) {
    const uploadedPhotoKeys: string[] = [];
    const localFilesToCleanup: string[] = [];

    try {
      const id = Number(req.params.id);
      if (!Number.isFinite(id)) {
        throw new AppError("Invalid id", 400);
      }

      const files = req.files as
        | { [field: string]: Express.Multer.File[] }
        | undefined;
      const add = files?.["servicePhotos"] ?? [];

      // userType for S3 folder
      const { rows: profileRows } = await db.query(AUTH.SELECT_BY_ID, [
        req.user?.id,
      ]);
      const accountType = profileRows[0]?.accountType;
      let userType: "user" | "agent" | "agency" = "user";
      if (accountType === "Individual") userType = "agent";
      else if (accountType === "Company/Agency/PropertyDeveloper")
        userType = "agency";

      // Upload new photos to S3 first
      if (add.length > 0) {
        await Promise.all(
          add.map(async (f) => {
            const localPath =
              (f as any).path || path.join("public", "documents", f.filename);
            localFilesToCleanup.push(localPath);

            const u = await uploadFileToS3(
              f.filename,
              f,
              userType,
              "service-photo"
            );
            uploadedPhotoKeys.push(u.fileKey);
            return u;
          })
        );
      }

      // Parse removePhotoIds
      let removePhotoIds: number[] = [];
      const raw = req.body?.removePhotoIds ?? [];
      if (Array.isArray(raw)) {
        removePhotoIds = raw.map((v) => Number(v)).filter(Number.isFinite);
      } else if (raw !== undefined) {
        const n = Number(raw);
        if (Number.isFinite(n)) removePhotoIds = [n];
      }

      const status = await this.statusRepo.getStatusDetailsByStatusName([
        "Activated",
      ]);
      if (!status) throw new AppError("Status 'Activated' not found", 400);

      const ctx = {
        profileId: Number(req.user?.id),
        statusId: Number(status[0].id || 1),
        createdBy: Number(req.user?.id),
      };

      const service = await this.repo.updatePhotos(id, uploadedPhotoKeys, ctx);

      await this.repo.deletePhotoseByIds(removePhotoIds);

      return service;
    } catch (err) {
      // Cleanup S3 files uploaded in this request
      await Promise.allSettled(
        uploadedPhotoKeys.map((k) => deleteFileFromS3(k))
      );

      throw err;
    }
  }

  async getUserMissionsAndTypes(id: number) {
    if (!Number.isFinite(id)) throw { statusCode: 400, message: "Invalid id" };

    const industries = await this.repo.userMissionsOrServices(id);

    const status = await this.statusRepo.getStatusDetailsByStatusName([
      "Activated",
      "Hidden",
    ]);

    const durations = await this.repo.getTypesByParentTypeName("durations");
    const locations = await this.repo.getLocations();

    return {
      industries,
      durations,
      locations,
      status,
    };
  }

  async getFilterOptionsAndTypes() {
    const status = await this.statusRepo.getStatusDetailsByStatusName([
      "Activated",
      "Hidden",
    ]);

    const durations = await this.repo.getTypesByParentTypeName("durations");
    const locations = await this.repo.getLocations();

    return {
      durations,
      locations,
      status,
    };
  }
}
