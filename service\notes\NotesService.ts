import { AppError } from "../../utils/error copy";
import { NoteRepository } from "../../repo/notes/NoteRepository";

export class NotesService {
  private noteRepo = new NoteRepository();

  async createPropertyNote(
    id: number,
    note: string,
    moduleName: string
  ) {
    if (!note || !note.trim()) throw new AppError("Note cannot be empty.", 400);
    return this.noteRepo.createNote(id, note, moduleName);
  }

  async getNotesByModuleNameAndId(id: number, moduleName: string) {
    return this.noteRepo.getNotesByEntityId(id, moduleName);
  }
}
