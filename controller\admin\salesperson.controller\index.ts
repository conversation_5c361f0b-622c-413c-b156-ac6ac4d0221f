import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import { AUTH } from "../../../utils/database/queries/auth";
import { TABLE } from "../../../utils/database/table";
import { Parser } from 'json2csv'; // npm install json2csv
import { sendEmailToReferalDetails } from "../../../utils/services/nodemailer/referalDetails";
 
// -------------------- GET ALL SALESPERSONS --------------------
export const getAllSalespersons = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      sortBy,
      filterColumn,
      filterValue,
      search,
      page = 1,
      pageSize = 10,
      status,
    } = req.query;

    let resolvedFilterValue: string | null = null;
    let resolvedFilterColumn: string | null = null;
    let resolvedStatusId: number | null = null;

    // ───── 1. Filter column logic ─────
    if (filterColumn === "created_at") {
      if (!filterValue || String(filterValue).trim() === "") {
        resolvedFilterColumn = null;
        resolvedFilterValue = null;
      } else {
        const monthNames = [
          "january", "february", "march", "april", "may", "june",
          "july", "august", "september", "october", "november", "december",
        ];
        const monthIndex = monthNames.indexOf(String(filterValue).toLowerCase());
        if (monthIndex === -1) {
          return error(res, 400, `Invalid month filter: '${filterValue}'`);
        }

        const year = new Date().getFullYear();
        const formattedMonth = String(monthIndex + 1).padStart(2, "0");
        resolvedFilterColumn = "created_at";
        resolvedFilterValue = `${year}-${formattedMonth}`;
      }
    } else if (filterColumn && filterValue) {
      resolvedFilterColumn = String(filterColumn);
      resolvedFilterValue = String(filterValue);
    }

    // ───── 2. Independent status filter ─────
    if (status && String(status).trim().toLowerCase() !== "all status") {
      const statusName = String(status).trim();
      const { rows } = await db.query(
        `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
        [statusName]
      );
      if (rows.length === 0) {
        return error(res, 400, `Status '${statusName}' not found.`);
      }
      resolvedStatusId = rows[0].id;
    }

    const sort = typeof sortBy === "string" ? sortBy : "id";
    const resolvedSearch = search ? String(search) : null;
    const resolvedPage = Number(page) || 1;
    const resolvedPageSize = Number(pageSize) || 10;

    const params = [
      1, // p_fnid
      null, // p_id
      null, // p_full_name
      null, // p_email
      null, // p_phone
      null, // p_referral_id
      null, // p_commission_rate
      resolvedStatusId, // p_statusid
      null, // p_notes
      sort, // p_sortby
      resolvedFilterColumn, // p_filter_column
      resolvedFilterValue, // p_filter_value
      resolvedSearch, // p_search
      resolvedPage, // p_page_no
      resolvedPageSize, // p_page_size
    ];

    const { rows } = await db.query(
      `SELECT * FROM look.sp_salesperson(${params.map((_, i) => `$${i + 1}`).join(", ")})`,
      params
    );

    const result = rows[0].sp_salesperson;

    if (result.type === "error") {
      return error(res, 400, result.message);
    }

    // Check that result.data has records
    if (!result.data || !Array.isArray(result.data.records)) {
      return error(res, 500, "Invalid response format: 'records' not found in data.");
    }

    const records = result.data.records;
    const pagination = result.data.pagination;

    // ───── 3. Count referrals for each salesperson ─────
    const salespersonIds = records.map((s: any) => s.id);

    if (salespersonIds.length > 0) {
   const { rows: referralCounts } = await db.query(
      `
     SELECT r."salesPersonId", COUNT(*) AS referrals
  FROM "referralRecords" r
  LEFT JOIN "commission" c ON c."referralId" = r.id
  WHERE r."salesPersonId" = ANY($1)
  GROUP BY r."salesPersonId"
      `,
      [salespersonIds]
    );

    const { rows: commissionSums } = await db.query(
    `SELECT "salesPersonId", COALESCE(SUM("commissionAmount"), 0) AS totalCommission
     FROM "commission"
     WHERE "salesPersonId" = ANY($1::bigint[])
     AND "status" IN ( 13)
     GROUP BY "salesPersonId"
     `,[salespersonIds]);


    const { rows: paidCommissionSums } = await db.query(
    `SELECT "salesPersonId", COALESCE(SUM("commissionAmount"), 0) AS paidCommission
     FROM "commission"
     WHERE "salesPersonId" = ANY($1::bigint[])
     AND "adminApproval"  = true
     GROUP BY "salesPersonId"
     `,[salespersonIds]);

   
      const referralMap = referralCounts.reduce((acc: any, curr: any) => {
        acc[curr.salesPersonId] = Number(curr.referrals);
        return acc;
      }, {});

     const commissionMap = commissionSums.reduce((acc: any, curr: any) => {
      const total = Number(curr.totalCommission || curr.totalcommission || 0);
      acc[curr.salesPersonId] = total;
      return acc;
    }, {});

    const paidCommissionMap = paidCommissionSums.reduce((acc: any, curr: any) => {
      const total = Number(curr.paidcommission || curr.paidCommission || 0);
      acc[curr.salesPersonId] = total;
      return acc;
    }, {});
 
      // Attach referral counts to each salesperson
      for (let salesperson of records) {
         salesperson.referrals = referralMap[salesperson.id] || 0;
        salesperson.totalCommission = commissionMap[salesperson.id] || 0;
        salesperson.paidCommission = paidCommissionMap[salesperson.id] || 0;
        salesperson.pendingCommission =  salesperson.totalCommission - salesperson.paidCommission;
      }
    }

    return responseData(res, 200, result.message, {
      records,
      pagination,
    });
  }
);


// -------------------- GET SALESPERSON BY ID --------------------
export const getSalespersonById = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Salesperson ID is required.");

    const params = [
      0, // p_fnid
      id,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ];

    const { rows } = await db.query(
      `SELECT * FROM look.sp_salesperson(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_salesperson;
    if (result.type === "error") return error(res, 400, result.message);
    if (!result.data) return error(res, 404, "Salesperson not found");

    return responseData(res, 200, result.message, result.data);
  }
);

// -------------------- CREATE SALESPERSON --------------------
export const createSalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      fullName,
      email,
      phone,
      referralId,
      commissionRate,
      status,
      notes,
    } = req.body;

    // Validate required fields
    if (!fullName || !email || !referralId || !commissionRate || !status) {
      return error(
        res,
        400,
        "Missing required fields: fullName, email, referralId, commissionRate, status"
      );
    }

    if (isNaN(Number(commissionRate)) || Number(commissionRate) <= 0) {
      return error(res, 400, "commissionRate must be a positive number.");
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Email & ReferralId must be unique
      const { rows: duplicates } = await client.query(
        `SELECT id FROM look.salespersons WHERE LOWER(email) = LOWER($1) OR LOWER(referral_id) = LOWER($2) LIMIT 1`,
        [email, referralId]
      );
      if (duplicates.length > 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Email or Referral ID already exists.");
      }

      // Validate status exists
      const statusNames = [status];
      const statusCheck = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusCheck.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Invalid status.");
      }

      const statusId = statusCheck.rows[0].id;

      const params = [
        2,
        null,
        fullName,
        email,
        phone,
        referralId,
        commissionRate,
        statusId,
        notes,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return responseData(res, 201, result.message, result.data);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Create salesperson failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- UPDATE SALESPERSON --------------------
export const updateSalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const {
      fullName,
      email,
      phone,
      referralId,
      commissionRate,
      status,
      notes,
    } = req.body;

    if (!id) return error(res, 400, "Salesperson ID is required.");
    if (!fullName || !email || !referralId || !commissionRate || !status) {
      return error(
        res,
        400,
        "Missing required fields: fullName, email, referralId, commissionRate, status"
      );
    }

    if (isNaN(Number(commissionRate)) || Number(commissionRate) <= 0) {
      return error(res, 400, "commissionRate must be a positive number.");
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Must exist
      const { rows: exists } = await client.query(
        `SELECT id FROM look.salespersons WHERE id = $1`,
        [id]
      );
      if (exists.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Salesperson not found.");
      }

      // Unique email/referralId (excluding self)
      const { rows: conflicts } = await client.query(
        `SELECT id FROM look.salespersons
       WHERE (LOWER(email) = LOWER($1) OR LOWER(referral_id) = LOWER($2)) AND id <> $3 LIMIT 1`,
        [email, referralId, id]
      );
      if (conflicts.length > 0) {
        await client.query("ROLLBACK");
        return error(
          res,
          400,
          "Another salesperson with this email or referralId already exists."
        );
      }

      // Check status exists
      const statusNames = [status];
      const statusCheck = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusCheck.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Invalid status.");
      }

      const statusId = statusCheck.rows[0].id;

      const params = [
        2,
        id,
        fullName,
        email,
        phone,
        referralId,
        commissionRate,
        statusId,
        notes,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return responseData(res, 200, result.message, result.data);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Update salesperson failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

// -------------------- DELETE SALESPERSON --------------------
export const deleteSalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Salesperson ID is required.");

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const params = [
        3, // fnid = delete
        id,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return response(res, 200, result.message);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Delete salesperson failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

export const updateSalespersonStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { status } = req.body;

    if (!id) return error(res, 400, "Salesperson ID is required.");
    if (!status) return error(res, 400, "Status is required.");

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // Check if the salesperson exists
      const { rows: exists } = await client.query(
        `SELECT id FROM look.salespersons WHERE id = $1`,
        [id]
      );
      if (exists.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Salesperson not found.");
      }

      // Validate status
      const statusNames = [status];
      const statusCheck = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusCheck.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Invalid status.");
      }

      const statusId = statusCheck.rows[0].id;

      const { rows: existing } = await client.query(
        `SELECT * FROM look.salespersons WHERE id = $1`,
        [id]
      );
      const current = existing[0];

      // Send all required fields from DB with updated status
      const params = [
        2,
        id,
        current.full_name,
        current.email,
        current.phone,
        current.referral_id,
        current.commission_rate,
        statusId,
        current.notes,
        "id",
      ];

      const { rows } = await client.query(
        `SELECT * FROM look.sp_salesperson(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const result = rows[0].sp_salesperson;
      if (result.type === "error") {
        await client.query("ROLLBACK");
        return error(res, 400, result.message);
      }

      await client.query("COMMIT");
      return responseData(res, 200, result.message, result.data);
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Update salesperson status failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);



export const getSalespersonReferals = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      search = '',
      page = 1,
      pageSize = 10,
      status,
      plan,
      monthAndYear,
    } = req.query;

    const { salespersonId } = req.params; // from /salespersons/:salespersonId/referrals

    if (!salespersonId) {
      return res.status(400).json({
        status: 400,
        success: false,
        message: 'salespersonId is required',
      });
    }

    const offset = (Number(page) - 1) * Number(pageSize);
    const limit = Number(pageSize);

    let conditions: string[] = [];
    let values: any[] = [];
    let idx = 1;

    // Always filter by salesperson
    conditions.push(`r."salesPersonId" = $${idx}`);
    values.push(salespersonId);
    idx++;

    // Search filter
    if (search) {
      conditions.push(`
        (
          sp.full_name ILIKE $${idx} OR
          sp.email ILIKE $${idx} OR
          sp.referral_id ILIKE $${idx} OR
          u."firstName" ILIKE $${idx} OR
          u."middleName" ILIKE $${idx} OR
          u."lastName" ILIKE $${idx} OR
          u.email ILIKE $${idx} OR
          u.phone ILIKE $${idx}
        )
      `);
      values.push(`%${search}%`);
      idx++;
    }

    // Status filter (commission status)
    if (status) {
      conditions.push(`st."name" ILIKE $${idx}`);
      values.push(status);
      idx++;
    }

    // Plan filter
    if (plan) {
      conditions.push(`s."package" ILIKE $${idx}`);
      values.push(plan);
      idx++;
    }

    // Month/year filter
    if (monthAndYear) {
      conditions.push(`TO_CHAR(r."created_at", 'Month YYYY') ILIKE $${idx}`);
      values.push(monthAndYear);
      idx++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

    // Main query
    const mainQuery = `
      SELECT 
        r.*, 
        r."created_at" AS referred_date,
        CASE WHEN r.id IS NULL THEN false ELSE true END AS "hasReferral",

        c.id AS commission_id,
        c."subscriptionId",
        c."commissionAmount",
        c."paidAt",
        c."status" AS commission_status_id,
        st."name" AS commission_status_name,
        c."adminApproval",
        c."referralId",

        sp.id AS salesperson_id,
        sp.full_name AS salesperson_name,
        sp.email AS salesperson_email,
        sp.phone AS salesperson_phone,
        sp.referral_id AS salesperson_referral_id,
        sp.commission_rate AS salesperson_commission_rate,

        u."firstName" AS user_firstname,
        u."middleName" AS user_middlename,
        u."lastName" AS user_lastname,
        u.email AS user_email,
        u.phone AS user_phone,

        s.id AS subscription_id,
        s."package" AS subscription_plan_name,
        s."price" AS subscription_price,
        s."paymentStatusId" AS subscription_paymentStatusId,
        s."startDate" AS subscription_startDate

      FROM "referralRecords" r
      LEFT JOIN "commission" c ON c."referralId" = r.id
      LEFT JOIN look.status st ON st.id = c."status"
      LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
      LEFT JOIN "${TABLE.PROFILE_TABLE}" u ON u.id = r."userId"
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      ${whereClause}
      ORDER BY r."created_at" DESC
      LIMIT $${idx} OFFSET $${idx + 1};
    `;

    values.push(limit, offset);

    // Count query
    const countQuery = `
      SELECT COUNT(*) 
      FROM "referralRecords" r
      LEFT JOIN "commission" c ON c."referralId" = r.id
      LEFT JOIN look.status st ON st.id = c."status"
      LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
      LEFT JOIN "${TABLE.PROFILE_TABLE}" u ON u.id = r."userId"
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      ${whereClause};
    `;

    // Month/year dropdown for this salesperson
    const monthYearQuery = `
      SELECT 
        TO_CHAR(r."created_at", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', r."created_at") AS month_date
      FROM "referralRecords" r
      WHERE r."salesPersonId" = $1
      GROUP BY month_year, month_date
      ORDER BY month_date DESC;
    `;

    // Available plans for this salesperson
    const availablePlansQuery = `
      SELECT DISTINCT s."package" AS plan_name
      FROM "referralRecords" r
      LEFT JOIN "commission" c ON c."referralId" = r.id
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      WHERE r."salesPersonId" = $1 AND s."package" IS NOT NULL
      ORDER BY s."package" ASC;
    `;
    const totalsQuery = `
    SELECT
      COUNT(DISTINCT r.id) AS total_referrals,
      COALESCE(SUM(CASE 
        WHEN c.status IN (3, 13, 30) AND c."paidAt" IS NULL 
        THEN c."commissionAmount" ELSE 0 END), 0) AS total_pending_commission,
      COALESCE(SUM(CASE 
        WHEN c.status IN (3, 13, 30) AND c."paidAt" IS NOT NULL 
        THEN c."commissionAmount" ELSE 0 END), 0) AS total_paid_commission,
      COALESCE(SUM(s.price), 0) AS total_subscription_value
    FROM "referralRecords" r
    LEFT JOIN "commission" c ON c."referralId" = r.id
    LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
    WHERE r."salesPersonId" = $1
  `;
    const [dataResult, countResult, monthYearResult, availablePlansResult,
      totalsResult] =
      await Promise.all([
        db.query(mainQuery, values),
        db.query(countQuery, values.slice(0, idx - 1)),
        db.query(monthYearQuery, [salespersonId]),
        db.query(availablePlansQuery, [salespersonId]),
        db.query(totalsQuery, [salespersonId]),
      ]);

    const total = Number(countResult.rows[0].count);
    const returned = dataResult.rows.length;

    res.status(200).json({
      status: 200,
      success: true,
      message: 'Salesperson referrals fetched successfully',
      data: dataResult.rows,
      pagination: {
        page: Number(page),
        total,
        pageSize: limit,
        returned,
      },
      availableMonths: monthYearResult.rows.map((row) =>
        row.month_year.trim()
      ),
      availablePlans: availablePlansResult.rows.map((row) => row.plan_name),
      totals: totalsResult.rows[0]
    });
  }
);


export const sendSalespersonReferralsCSV = asyncHandler(async (req, res) => {
  const { salespersonId } = req.params;
  const { plan, monthAndYear } = req.query;

  if (!salespersonId) {
    return res.status(400).json({
      status: 400,
      success: false,
      message: 'salespersonId is required',
    });
  }

  let conditions: string[] = [];
  let values: any[] = [];
  let idx = 1;

  // Always filter by salesperson
  conditions.push(`r."salesPersonId" = $${idx}`);
  values.push(salespersonId);
  idx++;

  // Plan filter
  if (plan) {
    conditions.push(`s."package" ILIKE $${idx}`);
    values.push(plan);
    idx++;
  }

  // Month/year filter
  if (monthAndYear) {
    conditions.push(`TO_CHAR(r."created_at", 'Month YYYY') ILIKE $${idx}`);
    values.push(monthAndYear);
    idx++;
  }

  const whereClause =
    conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  // Query (no pagination)
  const dataQuery = `
   SELECT 
  r.id AS referral_id,
  TO_CHAR(r."created_at", 'YYYY-MM-DD') AS referred_date,
  sp.full_name AS salesperson_name,
  sp.email AS salesperson_email,
  u."firstName" AS user_firstname,
  u."lastName" AS user_lastname,
  u.email AS user_email,
  u.phone AS user_phone,
  s."package" AS subscription_plan_name,
  s.price AS subscription_price,
  c."commissionAmount", 
  CASE 
    WHEN c."paymentMethod" IS NOT NULL THEN 'Paid'
    ELSE ''
  END AS commission_status_name,  
  c."paymentAt" AS commission_paid_at
FROM "referralRecords" r
LEFT JOIN "commission" c ON c."referralId" = r.id
LEFT JOIN look.status st ON st.id = c."status"
LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
LEFT JOIN "${TABLE.PROFILE_TABLE}" u ON u.id = r."userId"
LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
${whereClause}
ORDER BY r."created_at" DESC
  `;

  const result = await db.query(dataQuery, values);

  if (result.rows.length === 0) {
    return res.status(404).json({
      status: 404,
      success: false,
      message: 'No referrals found for this salesperson',
    });
  }

  // Convert to CSV
  const fields = [
    // { label: 'Referral ID', value: 'referral_id' },
    { label: 'Referred Date', value: 'referred_date' },
    { label: 'Salesperson Name', value: 'salesperson_name' },
    { label: 'Salesperson Email', value: 'salesperson_email' },
    { label: 'User First Name', value: 'user_firstname' },
    { label: 'User Last Name', value: 'user_lastname' },
    { label: 'User Email', value: 'user_email' },
    { label: 'User Phone', value: 'user_phone' },
    { label: 'Subscription Plan', value: 'subscription_plan_name' },
    { label: 'Subscription Price', value: 'subscription_price' },
    { label: 'Commission Amount', value: 'commissionAmount' },
    { label: 'Commission Status ID', value: 'commission_status_id' },
    { label: 'Commission Status', value: 'commission_status_name' },
    { label: 'Commission Paid At', value: 'commission_paid_at' },
  ];
  
  // Convert to CSV with custom headers
  const json2csvParser = new Parser({ fields });
  const csv = json2csvParser.parse(result.rows);
 

  // Email setup
  const salespersonEmail = result.rows[0].salesperson_email;
  if (!salespersonEmail) {
    return res.status(400).json({
      status: 400,
      success: false,
      message: 'Salesperson email not found',
    });
  }
 await sendEmailToReferalDetails(result.rows[0].salesperson_name, salespersonEmail,csv ,"Referral");
  

    

 return res.status(200).json({
    status: 200,
    success: true,
    message: `CSV report sent successfully to ${salespersonEmail}`,
  });
});


