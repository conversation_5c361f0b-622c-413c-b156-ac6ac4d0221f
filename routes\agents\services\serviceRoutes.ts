import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import { ServiceController } from "../../../controller/agents/services/ServiceController";

const router = Router();
const upload = storageData("documents");

const serviceController = new ServiceController();

// Multer fields for create/update
const fields = [{ name: "servicePhotos", maxCount: 10 }];

// LIST services (table + header counts via query params)
router.get("/", serviceController.getAllServices);

// GET one service
router.get("/:id", serviceController.getServiceById);

// CREATE or Update service
router.post(
  "/",
  upload.fields(fields),
  serviceController.createOrUpdateService
);

// UPDATE service (same handler; uses :id)
router.put(
  "/:id",
  upload.fields(fields),
  serviceController.createOrUpdateService
);

// UPDATE status (expects { statusId } in body)
router.put("/:id/status", upload.none(), serviceController.updateStatus);

// DELETE service
router.delete("/:id", serviceController.deleteService);

// ADD/REMOVE photos (send servicePhotos[] files and/or removePhotoIds in body)
router.patch(
  "/:id/photos",
  upload.fields(fields),
  serviceController.updatePhotos
);

export default router;
