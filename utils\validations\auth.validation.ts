import { z } from "zod";

// Registration Schema
export const registerSchema = z
  .object({
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    phoneNumber: z
      .string()
      .min(10, "Phone number must be at least 10 characters long")
      .optional(),
    email: z.string().email("Invalid email address"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/\d/, "Password must contain at least one number")
      .regex(
        /[@$!%*?&#]/,
        "Password must contain at least one special character"
      )
      .optional(),
    confirmPassword: z.string().optional(),
    accountId: z.string().optional(),
    accountType: z.string().optional(),
    userName: z.string().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Password and Confirm Password do not match",
    path: ["confirmPassword"],
  });

export const agentRegisterSchema = z.object({
  accountType: z
    .string({
      error: "Account type is required",
    })
    .min(1, "Account type is required"),
  email: z.email({error: (issue) => {
        if (issue.input === null || issue.input === undefined) {
          return "Email is required.";
        }
        return "Invalid email format.";
      }
    }),
  password: z
    .string({
      error: "Password is required",
    })
    .min(8, "Password must be at least 8 characters long")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number")
    .regex(
      /[@$!%*?&#]/,
      "Password must contain at least one special character"
    ),
  confirmPassword: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Password and Confirm Password do not match",
  path: ["confirmPassword"],
});

// Login Schema
export const loginSchema = z.object({
  email: z.string({ error: "Username or email is required" })
    .min(1, "Username or email is required"),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/\d/, "Password must contain at least one number")
    .regex(
      /[@$!%*?&#]/,
      "Password must contain at least one special character"
    ),
});

// Forgot Password Schema
export const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

// Reset Password Schema
export const resetPasswordSchema = z
  .object({
    newPassword: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/\d/, "Password must contain at least one number")
      .regex(
        /[@$!%*?&#]/,
        "Password must contain at least one special character"
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Password and confirmation password must match.",
    path: ["confirmPassword"],
  });

export const updateUserProfileSchema = z.object({
  userName: z
    .string()
    .min(3, { message: "User name must be at least 3 characters long." })
    .optional(),

  firstName: z
    .string()
    .min(2, { message: "First name must be at least 3 characters long." })
    .optional(),

  lastName: z
    .string()
    .min(2, { message: "Last name must be at least 3 characters long." })
    .optional(),

  designation: z.string().nullable().optional(),
  shortDescription: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  specialization: z.string().nullable().optional(),

  experience: z
    .number()
    .nullable()
    .optional()
    .refine((val) => val === null || val! >= 0, {
      message: "Experience must be a positive number or null.",
    }),

  languages: z.string().nullable().optional(),
  industry: z.string().nullable().optional(),

  accountType: z
    .string()
    .min(1, { message: "Account type is required." })
    .nullable()
    .optional(),

  association: z
    .boolean({ error: "Association status is required." })
    .nullable()
    .optional(),

  certified: z
    .boolean({ error: "Certified status is required." })
    .nullable()
    .optional(),

  issuedBy: z.string().nullable().optional(),
  certificateNumber: z.string().nullable().optional(),
  expiryDate: z.date().nullable().optional(),

  contactNumber: z
    .string()
    .min(10, { message: "Contact number must be at least 10 characters long." })
    .nullable()
    .optional(),

  whatsappContact: z
    .string()
    .min(10, { message: "Contact number must be at least 10 characters long." })
    .nullable()
    .optional(),

  contactEmail: z
    .string()
    .email({ message: "Invalid email format." })
    .nullable()
    .optional(),

  cardHolderName: z
    .string()
    .min(3, { message: "Card holder name must be at least 3 characters long." })
    .nullable()
    .optional(),

  cardType: z
    .string()
    .min(1, { message: "Card type is required." })
    .nullable()
    .optional(),

  cardNumber: z.bigint().nullable().optional(),
});

export const changePasswordSchema = z
  .object({
    oldPassword: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/\d/, "Password must contain at least one number")
      .regex(
        /[@$!%*?&#]/,
        "Password must contain at least one special character"
      ),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/\d/, "Password must contain at least one number")
      .regex(
        /[@$!%*?&#]/,
        "Password must contain at least one special character"
      ),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords must match",
    path: ["confirmPassword"],
  });

export const emailSchema = z.object({
  email: z.email({error: (issue) => {
        if (issue.input === null || issue.input === undefined) {
          return "Email is required.";
        }
        return "Invalid email format.";
      }
    })
    .max(254, { message: "Email is too long" })
    .min(5, { message: "Email is too short" })
    .transform((val) => val.trim().toLowerCase()),
});

export const userNameSchema = z.object({
  userName: z
    .string({ error: "Username is required" })
    .min(3, { message: "Username must be at least 3 characters" })
    .max(30, { message: "Username must be at most 30 characters" })
    .regex(/^[a-zA-Z0-9_\.]+$/, {
      message:
        "Username can only contain letters, numbers, underscores, and dots",
    }),
});

export const updatePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Must contain at least one uppercase letter")
    .regex(/[a-z]/, "Must contain at least one lowercase letter")
    .regex(/[0-9]/, "Must contain at least one number")
    .regex(/[^A-Za-z0-9]/, "Must contain at least one special character"),
});
