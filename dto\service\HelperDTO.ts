export const normalizeToArray = (val: any): number[] => {
  if (val === undefined || val === null || val === "") return [];

  // If already an array
  if (Array.isArray(val)) {
    return val.map((v) => Number(v)).filter(Number.isFinite);
  }

  // If string with commas → split into array
  if (typeof val === "string" && val.includes(",")) {
    return val
      .split(",")
      .map((v) => Number(v.trim()))
      .filter(Number.isFinite);
  }

  // Otherwise, handle as single value
  return [Number(val)].filter(Number.isFinite);
};
