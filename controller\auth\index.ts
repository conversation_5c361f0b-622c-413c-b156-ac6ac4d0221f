import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import { AUTH } from "../../utils/database/queries/auth";
import { db } from "../../config/database";
import {
  agentRegisterSchema,
  emailSchema,
  loginSchema,
  registerSchema,
  resetPasswordSchema,
  updatePasswordSchema,
  updateUserProfileSchema,
  userNameSchema,
} from "../../utils/validations/auth.validation";
import {
  errorCatchResponse,
  errorResponse,
  response,
  responseData,
} from "../../utils/response";
import { getSignedJwt, verifyToken } from "../../utils/services/jwt";
import {
  generateOTP,
  resendVerificationEmail,
  sendRegistrationEmail,
} from "../../utils/services/nodemailer/register";
import asyncHandler from "../../middleware/trycatch";
import fs from "fs";
import {
  ACCOUNT_TYPE,
  AGENT_ACCOUNT_TYPE,
} from "../../utils/enums/account.enum";
import { sendVerificationEmailOnLoginAttempt } from "../../utils/services/nodemailer/accountVerification";
import { sendForgotPasswordOTP } from "../../utils/services/nodemailer/forgetPassword";
import { ROLE_QUERIES } from "../../utils/database/queries/roles";
import ROLES from "../../utils/enums/role.enum";
import { upsertToken } from "../../utils/helperFunctions/upsertToken";
import validate from "../../utils/validations";
import { UpdateUserProfile } from "../../utils/types/user";
import type { JwtPayload } from "jsonwebtoken";
import { AGENTS } from "../../utils/database/queries/agentDetails";
import { generateUniqueUsername } from "../../utils/helperFunctions/userNameValidator";
import { TABLE } from "../../utils/database/table";
import { nanoid } from "nanoid";
import { REFERRALS } from "../../utils/database/queries/referrals";
const baseUrl = process.env.BASE_URL?.replace(/\/+$/, "");
import CryptoJS from 'crypto-js';
// Register User
export const register = asyncHandler(async (req: Request, res: Response) => {
  try {
    const {
      accountId,
      accountType,
      userName,
      firstName,
      lastName,
      email,
      password,
      confirmPassword,
      phoneNumber,
    } = req.body;

    const phone = phoneNumber;

    if (accountType === ACCOUNT_TYPE.EMAIL) {
      const { success, error } = registerSchema.safeParse({
        email,
        password,
        confirmPassword,
        phoneNumber,
        userName,
      });

      if (!success) {
        return errorResponse(res, error?.issues[0].message);
      }
      const alphabetNumberRegex = /^(?=.*[A-Za-z])[A-Za-z0-9]+$/;
      if (
        !userName.trim() ||
        userName === "" ||
        !alphabetNumberRegex.test(userName)
      ) {
        return errorResponse(res, "User name must contain alphabets.");
      }
      if (!userName.trim() || userName === "") {
        return errorResponse(res, "User name is required ");
      }
      if (userName.length < 3) {
        return errorResponse(
          res,
          "User name must be at least 3 characters long"
        );
      }
    } else if (accountType === ACCOUNT_TYPE.GOOGLE) {
      const { success, error } = registerSchema.safeParse({
        email,
        firstName,
        accountId,
        lastName,
      });
      if (!success) {
        return errorResponse(res, error?.issues[0].message);
      }
    }

    const statusNames = ["Activated"];
    const activatedStatus = await db.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );

    const statusId = activatedStatus.rows[0].id;

    const result = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
    if (result.rows[0] && result.rows.length > 0) {
      const existingUser = await result.rows[0];

      const existingLogin = await db.query(
        AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN,
        [existingUser.id]
      );

      const existingLoginUser = existingLogin.rows[0];
      if (!existingLoginUser?.isActivated) {
        if (accountType === ACCOUNT_TYPE.GOOGLE) {
          const token = await getSignedJwt(existingLoginUser.id, email);
          res.cookie("authToken", token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
            maxAge:
              (Number(process.env.COOKIE_EXPIRY) || 7) * 24 * 60 * 60 * 1000,
          });

          await upsertToken(existingLoginUser.id, token);
          existingUser.user_id = existingLoginUser.id;

          const existingAgent2 = await db.query(
            AGENTS.CHECK_EXISTING_ACCOUNT_DETAILS,
            [existingUser.user_id]
          );
          let agentProfile = "user";
          if (existingAgent2.rows.length > 0) {
            agentProfile = "agent";
          }
          const data = { ...existingUser, agentProfile };

          return responseData(res, 200, "Login successful", data);
        }

        if (existingLoginUser?.expireOn > new Date()) {
          return errorResponse(
            res,
            "OTP already sent. Please check your email."
          );
        } else {
          const otp = generateOTP();
          const expireTime = new Date(Date.now() + 10 * 60 * 1000);
          await db.query(AUTH.UPDATE_OTP_INTO_LOGIN, [
            otp,
            expireTime,
            existingLoginUser.id,
          ]);
          await sendRegistrationEmail(firstName, email, res, otp);
        }
      } else {
        // if (accountType === ACCOUNT_TYPE.EMAIL) {
        //   const UserNameCheck = await db.query(
        //     AUTH.SELECT_BY_USERNAME_FROM_PROFILE,
        //     [userName]
        //   );

        //   console.log(UserNameCheck.rows);
        //   if (UserNameCheck.rows.length > 0) {
        //     const existingUser3 = UserNameCheck.rows[0];
        //     console.log(existingUser3);
        //     const userNameStrlized = existingUser3.username;
        //     if (
        //       userName.toLowerCase().trim() === userNameStrlized.toLowerCase()
        //     ) {
        //       return errorResponse(res, "User Name Aleady Exist");
        //     }
        //   } else {
        //     return errorResponse(res, "Email Aleady Exist");
        //   }
        // } else {
        if (accountType === ACCOUNT_TYPE.GOOGLE) {
          const token = await getSignedJwt(existingLoginUser.id, email);
          res.cookie("authToken", token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
            maxAge:
              (Number(process.env.COOKIE_EXPIRY) || 7) * 24 * 60 * 60 * 1000,
          });
          await upsertToken(existingLoginUser.id, token);
          existingUser.user_id = existingUser.id;

          const existingAgent2 = await db.query(
            AGENTS.CHECK_EXISTING_ACCOUNT_DETAILS,
            [existingUser.user_id]
          );
          let agentProfile = "user";
          if (existingAgent2.rows.length > 0) {
            agentProfile = "agent";
          }
          const data = { ...existingUser, agentProfile };

          return responseData(res, 200, "Login successful", data);
        }
        // }
      }
    } else {
      const role = await db.query(ROLE_QUERIES.GET_FIRST_ROLE, [ROLES.USER]);

      const roleId = role.rows[0].id;

      // Creating User using email
      if (accountType === ACCOUNT_TYPE.EMAIL) {
        const UserNameCheck = await db.query(
          AUTH.SELECT_BY_USERNAME_FROM_PROFILE,
          [userName]
        );

        if (UserNameCheck.rows.length > 0) {
          const existingUser3 = UserNameCheck.rows[0];

          const userNameStrlized = existingUser3.username;
          if (
            userName.toLowerCase().trim() === userNameStrlized.toLowerCase()
          ) {
            return errorResponse(res, "User Name Aleady Exist");
          } else {
          }
        }
        const { query, values } = AUTH.INSERT_INTO_PROFILE({
          email,
          phone,
          statusId,
        });

        const result = await db.query(query, values);

        if (result.rows.length > 0) {
          const user = result.rows[0];
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(password, salt);

          const otp = generateOTP();
          const expireTime = new Date(Date.now() + 10 * 60 * 1000);
          const loginResult = await db.query(AUTH.INSERT_INTO_LOGIN, [
            user.id,
            userName,
            accountType,
            hashedPassword,
            otp,
            expireTime,
          ]);
          if (loginResult.rows.length > 0) {
            await db.query(AUTH.INSERT_INTO_LOGIN_ROLE, [
              loginResult?.rows[0]?.id,
              roleId,
              loginResult?.rows[0]?.id,
            ]);
            await sendRegistrationEmail(userName, email, res, otp);
          }
        } else {
          return response(res, 500, "Failed to send OTP");
        }
      } else {
        const { query, values } = AUTH.INSERT_INTO_PROFILE({
          email,
          firstName,
          lastName,
          statusId,
        });

        const result = await db.query(query, values);

        const newUserName = email.split("@")[0];
        if (result.rows.length > 0) {
          const user = result.rows[0];
          const loginResult = await db.query(
            AUTH.INSERT_INTO_LOGIN_WITH_STATUS,
            [user.id, newUserName, accountType, accountId, true]
          );

          if (loginResult.rows.length > 0) {
            await db.query(AUTH.INSERT_INTO_LOGIN_ROLE, [
              loginResult?.rows[0]?.id,
              roleId,
              loginResult?.rows[0]?.id,
            ]);
            const token = getSignedJwt(loginResult?.rows[0]?.id, email);
            res.cookie("authToken", token, {
              httpOnly: true,
              secure: process.env.NODE_ENV === "production",
              sameSite: "strict",
              maxAge:
                (Number(process.env.COOKIE_EXPIRY) || 7) * 24 * 60 * 60 * 1000,
            });
            await upsertToken(loginResult?.rows[0]?.id, token);
            user.user_id = user.id;

            console.log(user);

            const existingAgent2 = await db.query(
              AGENTS.CHECK_EXISTING_ACCOUNT_DETAILS,
              [user.user_id]
            );
            let agentProfile = "user";
            if (existingAgent2.rows.length > 0) {
              agentProfile = "agent";
            }
            const data = { ...user, agentProfile };
            return responseData(res, 200, "Login successful", data);
          }
        }
      }
    }
  } catch (error) {
    console.error("Error creating user profile:", error);
    return response(res, 500, "Failed to register user");
  }
});

export const forgotPasswordOTP = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { email } = req.body;

      // Check if the user exists
      const results = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
      if (results.rows.length === 0) {
        return errorResponse(
          res,
          "No account found with the provided email address."
        );
      }

      const user = results.rows[0];

      // Check if an OTP exists for this user
      const loginResult = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        user.id,
      ]);
      if (loginResult.rows.length > 0) {
        const loginData = loginResult.rows[0];
        if (loginData.expireOn > new Date()) {
          return errorResponse(
            res,
            "An OTP has already been sent. Please check your email."
          );
        }
        const newOtp = generateOTP();
        const expireTime = new Date(Date.now() + 10 * 60 * 1000);

        await db.query(AUTH.UPDATE_OTP_INTO_LOGIN, [
          newOtp,
          expireTime, //@ts-ignore
          loginData.id,
        ]);

        //@ts-ignore
        await sendForgotPasswordOTP(loginData?.username, email, res, newOtp);
      }
    } catch (error) {
      console.error("Error sending forgot password OTP:", error);
      return response(res, 500, "Failed to send OTP for password reset.");
    }
  }
);

export const updatePasswordWithOTP = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { email, newPassword, confirmPassword } = req.body;
      const { success, error } = resetPasswordSchema.safeParse({
        newPassword,
        confirmPassword,
      });

      if (!success) {
        return errorResponse(res, error.issues[0].message);
      }

      // Check if the user exists
      const userResult = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
      if (userResult.rows.length === 0) {
        return errorResponse(res, "No account found with the provided email.");
      }
      const user = userResult.rows[0];

      // Hash new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // Update password in database
      await db.query(AUTH.UPDATE_PASSWORD, [hashedPassword, user.id]);

      return response(
        res,
        200,
        "Password updated successfully. You can now log in."
      );
    } catch (error) {
      console.error("Error updating password:", error);
      return response(res, 500, "Failed to update password.");
    }
  }
);

export const verifyOTP = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { email, otp } = req.body;
    const result = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
    if (result.rows.length > 0) {
      const user = result.rows[0];
      const otpResult = await db.query(
        AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN_WITH_OTP,
        [user.id, otp]
      );

      if (otpResult.rows.length > 0) {
        const otp = otpResult.rows[0];

        await db.query(AUTH.UPDATE_OTP_AND_STATUS_INTO_LOGIN, [
          null,
          null, //@ts-ignore
          user.id,
        ]);

        if (otp.expireOn > new Date()) {
          return response(res, 200, "OTP verified successfully");
        } else {
          return errorResponse(
            res,
            "OTP has expired. Please request a new OTP"
          );
        }
      } else {
        return errorResponse(res, "Invalid OTP");
      }
    } else {
      return errorResponse(res, "Account not found");
    }
  } catch (error) {
    console.error("Error verifying OTP:", error);
    return response(res, 500, "Failed to verify OTP");
  }
});

export const validateEmail = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const validationResult = emailSchema.safeParse(req.body);
      // Check if validation failed
      if (!validationResult.success) {
        const errorMessages = validationResult.error.issues.map(
          (issue) => issue.message
        );
        return response(res, 400, errorMessages);
      }
      // Extract validated email from the data (now type-safe)
      const validatedEmail = validationResult.data.email;
      const results = await db.query(AUTH.SELECT_BY_EMAIL, [validatedEmail]);
      if (results.rows.length > 0) {
        return response(res, 400, "Email already exists");
      }

      return response(res, 200, "Valid Email");
    } catch (error) {
      return response(res, 500, "Internal server error while validating email");
    }
  }
);

export const validateUserName = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const validationResult = userNameSchema.safeParse(req.body);
      // Check if validation failed
      if (!validationResult.success) {
        const errorMessages = validationResult.error.issues.map(
          (issue) => issue.message
        );
        return response(res, 400, errorMessages);
      }
      // Extract validated email from the data (now type-safe)
      const validatedUserName = validationResult.data.userName;

      const UserNameCheck = await db.query(
        AUTH.SELECT_BY_USERNAME_FROM_PROFILE,
        [validatedUserName]
      );

      if (UserNameCheck.rows.length > 0) {
        errorResponse(res, "User Name Aleady Exist");
        return;
      }

      return response(res, 200, "Valid User Name");
    } catch (error) {
      return response(
        res,
        500,
        "Internal server error while validating user name"
      );
    }
  }
);

export const accounrtVerifyOTP = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { email, otp } = req.body;
      const result = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
      if (result.rows.length > 0) {
        const user = result.rows[0];
        const otpResult = await db.query(
          AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN_WITH_OTP,
          [user.id, otp]
        );

        const loginTableResult = await db.query(
          AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN,
          [user.id]
        );

        if (loginTableResult.rows.length > 0) {
          if (loginTableResult.rows[0].isActivated) {
            return response(res, 200, "Account already verified");
          }
        }

        if (otpResult.rows.length > 0) {
          const otp = otpResult.rows[0];
          if (otp.expireOn > new Date()) {
            const loginResult = await db.query(AUTH.UPDATE_INTO_LOGIN, [
              user.id,
            ]);
            if (loginResult.rows.length > 0) {
              await db.query(AUTH.UPDATE_OTP_AND_STATUS_INTO_LOGIN, [
                null,
                null, //@ts-ignore
                user.id,
              ]);

              return response(res, 200, "Account verified successfully");
            }
          } else {
            return errorResponse(
              res,
              "OTP has expired. Please request a new OTP"
            );
          }
        } else {
          return errorResponse(res, "Invalid OTP");
        }
      } else {
        return errorResponse(res, "Account not found");
      }
    } catch (error) {
      console.error("Error verifying account:", error);
      return response(res, 500, "Failed to verify account");
    }
  }
);

export const resendOTP = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { email } = req.body;
    if (!email) {
      return errorResponse(res, "Email is required");
    }
    const results = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
    if (results.rows.length > 0) {
      const user = results.rows[0];
      const otpResult = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        user.id,
      ]);

      // if (otpResult.rows.length > 0) {
      //   const otp = otpResult.rows[0];
      //   if (otp.expireOn > new Date()) {
      //     return errorResponse(
      //       res,
      //       "OTP already sent. Please check your email."
      //     );
      //   }
      // }

      const otp = generateOTP();
      const expireTime = new Date(Date.now() + 10 * 60 * 1000);
      await db.query(AUTH.UPDATE_OTP_INTO_LOGIN, [
        otp,
        expireTime,
        otpResult.rows[0].id,
      ]);
      await resendVerificationEmail(email, res, otp);
      return response(res, 201, "OTP has been sent to your email");
    } else {
      return errorResponse(
        res,
        "No account found with the provided email address."
      );
    }
  } catch (error) {
    console.error("Error resending OTP:", error);
    return response(res, 500, "Failed to resend OTP");
  }
});

export const login = asyncHandler(async (req: Request, res: Response) => {
  try {
    let result;
    let resultUserName;
    const { email, password } = req.body;
    const { success, error } = loginSchema.safeParse({ email, password });
    if (!success) {
      return errorResponse(res, error?.issues[0].message);
    }
    result = await db.query(AUTH.SELECT_BY_EMAIL, [email]);

    if (result.rows.length === 0) {
      resultUserName = await db.query(AUTH.SELECT_BY_USERNAME_FROM_PROFILE, [
        email,
      ]);
      if (resultUserName.rows.length === 0) {
        return errorResponse(res, "Invalid username or email.");
      } else {
        result = await db.query(AUTH.SELECT_BY_ID, [
          resultUserName?.rows[0].id,
        ]);
      }
    }

    if (result.rows.length > 0) {
      const user = result.rows[0];

      const loginResult = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        user.id,
      ]);
      if (loginResult.rows.length > 0) {
        const login = loginResult.rows[0];
        if (login.passwordHash) {
          if (await bcrypt.compare(password, login.passwordHash)) {
            if (login.isActivated) {
              const token = getSignedJwt(login.id, email);
              res.cookie("authToken", token, {
                httpOnly: true,
                secure: process.env.NODE_ENV === "production",
                sameSite: "strict",
                maxAge:
                  (Number(process.env.COOKIE_EXPIRY) || 7) *
                  24 *
                  60 *
                  60 *
                  1000,
              });
              await upsertToken(login.id, token);

              const result = await db.query(AUTH.UPDATE_INTO_LOGIN_LAST_LOGIN, [login.id]);
              if (result.rows.length > 0) {
                console.log("lastLogin updated successfully");
              } else {
                console.log("Failed to update lastLogin");
              }

              delete login.passwordHash;
              user.user_id = user.id;
              delete user.id;
              login.login_id = login.id;
              delete login.id;

              if (user.profileImage) {
                user.profileImage = baseUrl + user.profileImage;
              }

              const existingAgent2 = await db.query(
                AGENTS.CHECK_EXISTING_ACCOUNT_DETAILS,
                [user.user_id]
              );
              let agentProfile = "user";
              if (existingAgent2.rows.length > 0) {
                agentProfile = "agent";
              }
              const data = { ...user, ...login, agentProfile };

              return responseData(res, 200, "Login successful", data);
            } else {
              const otp = generateOTP();
              const expireTime = new Date(Date.now() + 10 * 60 * 1000);
              await db.query(AUTH.UPDATE_OTP_INTO_LOGIN, [
                otp,
                expireTime,
                login.id,
              ]);

              await sendVerificationEmailOnLoginAttempt(
                login.username,
                email!,
                res,
                otp
              );
            }
          } else {
            return errorResponse(res, "Invalid email or password");
          }
        } else {
          return errorResponse(res, "You have not set password yet.");
        }
      } else {
        return errorResponse(res, "Invalid credentials. Please try again.");
      }
    } else {
      return errorResponse(res, "Invalid credentials. Please try again.");
    }
  } catch (error) {
    console.error("Error logging in user:", error);
    return response(res, 500, "Failed to login user");
  }
});

export const changeOrResetPassword = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { currentPassword, newPassword, confirmPassword } = req.body;
      const { success, error } = resetPasswordSchema.safeParse({
        newPassword,
        confirmPassword,
      });

      if (!success) {
        return errorResponse(res, error.issues[0].message);
      }

      // Check if the user exists
      const userResult = await db.query(AUTH.SELECT_BY_ID, [req.user.id]);
      if (userResult.rows.length === 0) {
        return errorResponse(res, "No account found.");
      }
      const user = userResult.rows[0];

      const loginResult = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        user.id,
      ]);
      if (loginResult.rows.length === 0) {
        return errorResponse(res, "No account found.");
      }
      const loginUser = loginResult.rows[0];

      // Compare provided password with stored hashed password
      const isMatched = await bcrypt.compare(
        currentPassword,
        loginUser.passwordHash
      );

      if (!isMatched) {
        return errorResponse(res, "Current password is incorrect.");
      }

      // Hash new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // Update password in database
      await db.query(AUTH.UPDATE_PASSWORD, [hashedPassword, user.id]);

      return response(res, 200, "Password updated successfully.");
    } catch (error) {
      console.error("Error updating password:", error);
      return response(res, 500, "Failed to update password.");
    }
  }
);

export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  try {
    // Check if the user exists
    const userResult = await db.query(AUTH.SELECT_PROFILE_WITH_LOGIN, [
      req.user.loginId,
    ]);

    if (userResult.rows.length === 0) {
      return errorResponse(res, "No account found.");
    }

    let user = userResult.rows[0];

    delete user.passwordHash;
    if (user.profileImage) {
      user.profileImage = baseUrl + user.profileImage;
    }

    return responseData(res, 200, "Data fetched", user);
  } catch (error) {
    console.error("Error get profile:", error);
    return response(res, 500, "Failed to get profile.");
  }
});

export const logout = asyncHandler(async (req: Request, res: Response) => {
  try {
    // Check if the user exists
    const userResult = await db.query(
      AUTH.SELECT_PROFILE_WITH_LOGIN_AND_LOGIN_ROLE,
      [req.user.loginId]
    );

    if (userResult.rows.length === 0) {
      return errorResponse(res, "No account found.");
    }

    // Remove the token from the database
    await db.query(AUTH.DELETE_INTO_TOKEN_LOGIN, [req.user.loginId]);
    res.cookie("authToken", "", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 0,
    });
    return response(res, 200, "Logout successful");
  } catch (error) {
    console.error("Error during logout:", error);
    return errorResponse(res, "Failed to log out.");
  }
});

export const updateProfile = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user?.loginId;

      const file = req.file as Express.Multer.File;

      const {
        userName,
        firstName,
        lastName,
        phoneNumber,
        designation,
        shortDescription,
        description,
        specialization,
        experience,
        languages,
        industry,
        accountType,
        association,
        certified,
        issuedBy,
        certificateNumber,
        expiryDate,
        contactNumber,
        whatsappContact,
        contactEmail,
        cardHolderName,
        cardType,
        cardNumber,
      } = req.body;

      const data = {
        userName,
        firstName,
        lastName,
        phoneNumber,
        designation,
        shortDescription,
        description,
        specialization,
        experience: experience ? Number(experience) : null,
        languages,
        industry,
        accountType,
        association:
          typeof association === "string"
            ? association === "true"
            : !!association,
        certified:
          typeof certified === "string" ? certified === "true" : !!certified,
        issuedBy,
        certificateNumber,
        expiryDate: expiryDate ? new Date(expiryDate) : null,
        contactNumber,
        whatsappContact,
        contactEmail,
        cardHolderName: cardHolderName || null,
        cardType: cardType || null,
        cardNumber: cardNumber ? BigInt(cardNumber) : null,
      };

      const validation = validate(updateUserProfileSchema, data, res);

      if (!validation.success) {
        if (file) {
          fs.unlinkSync(`public/users/${file.filename}`);
        }
        return;
      }

      const result = await db.query(AUTH.SELECT_PROFILE_WITH_LOGIN, [userId]);

      if (result.rowCount === 0) {
        if (file) {
          fs.unlinkSync(`public/users/${file.filename}`);
        }
        return response(res, 404, "User not found");
      }

      const existUserName = await db.query(AUTH.GET_USER_BY_USERNAME, [
        userName,
        userId,
      ]);

      if (existUserName?.rowCount && existUserName.rowCount > 0) {
        errorResponse(res, "User name already taken");
        return;
      }

      let userDetails: UpdateUserProfile = {
        firstName,
        lastName,
        phone: phoneNumber,
        designation: designation || null,
        shortDescription: shortDescription || null,
        description: description || null,
        specialization: specialization || null,
        experience: experience ? Number(experience) : null,
        languages: languages || null,
        industry: industry || null,
        accountType: accountType || null,
        association:
          typeof association === "string"
            ? association === "true"
            : !!association,
        certified:
          typeof certified === "string" ? certified === "true" : !!certified,
        issuedBy: issuedBy || null,
        certificateNumber: certificateNumber || null,
        expiryDate: expiryDate ? new Date(expiryDate) : null,
        contactNumber: contactNumber || null,
        whatsappContact: whatsappContact || null,
        contactEmail: contactEmail || null,
        cardHolderName: cardHolderName || null,
        cardType: cardType || null,
        cardNumber: cardNumber ? BigInt(cardNumber) : null,
        profileImage: null,
        id: null,
      };

      // Update profileImage if file is present
      if (file) {
        if (result.rows[0].profileImage) {
          fs.unlinkSync(`public/${result.rows[0].profileImage}`);
        }
        userDetails.profileImage = `/users/${file.filename}`;
      } else {
        if (result.rows[0].profileImage) {
          userDetails.profileImage = result.rows[0].profileImage;
        }
      }

      // Finally, assign ID at the end
      userDetails.id = req.user?.id as number;

      // Insert new user details
      await db.query(
        AUTH.UPDATE_USER_DETAIL(userDetails),
        Object.values(userDetails)
      );

      const userData = await db.query(AUTH.SELECT_PROFILE_WITH_LOGIN, [userId]);

      let user = userData.rows[0];

      delete user.passwordHash;

      if (user.profileImage) {
        user.profileImage = baseUrl + user.profileImage;
      }
      return responseData(
        res,
        200,
        "Profile updated successfully",
        userData.rows[0]
      );
    } catch (error) {
      const file = req.file as Express.Multer.File;

      if (file) {
        fs.unlinkSync(`public/users/${file.filename}`);
      }

      console.log("Error while updateing profile", error);
      return errorResponse(res, "Something wents wrong!");
    }
  }
);

export const accountActivatedDeactivated = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const statusNames = ["Activated", "Deactivated"];

      const allStatuses = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      const statuses = allStatuses.rows;

      // Find the remaining status (not the current one)
      const remainingStatus = statuses.find(
        (status) => status.id !== req.user.statusId
      );

      if (!remainingStatus) {
        return errorResponse(
          res,
          "No alternate status found for update status."
        );
      }

      // Insert new user details
      await db.query(AUTH.UPDATE_PROFILE_STATUS, [
        remainingStatus.id,
        req.user?.id,
      ]);

      return response(
        res,
        200,
        `Account status changed to ${remainingStatus?.name?.toLowerCase()} successfully.`
      );
    } catch (error) {
      console.error("Error during status update:", error);
      return errorResponse(res, "Failed to status update.");
    }
  }
);

export const updatePassword = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Validate request body
      const validationResult = updatePasswordSchema.safeParse(req.body);

      if (!validationResult.success) {
        const errors = validationResult.error.issues.map((err) => ({
          field: err.path[0],
          message: err.message,
        }));
        return errorResponse(res, "Validation failed");
      }

      const { currentPassword, newPassword } = validationResult.data;
      const userId = req.user?.id;
      const userResult = await db.query(
        AUTH.SELECT_PASSWORD_BY_PROFILE_ID_FROM_LOGIN,
        [userId]
      );

      if (userResult.rows.length === 0) {
        return errorResponse(res, "User not found");
      }

      const currentHashedPassword = userResult.rows[0].password;

      console.log(userResult.rows[0].accountType);

      if (
        !currentHashedPassword &&
        userResult.rows[0].accountType != ACCOUNT_TYPE.GOOGLE
      ) {
        // 2. Verify current password
        const isMatch = await bcrypt.compare(
          currentPassword,
          currentHashedPassword
        );

        if (!isMatch) {
          return errorResponse(res, "Current password is incorrect");
        }
      }

      // 3. Check if new password is different
      if (currentPassword === newPassword) {
        return errorResponse(
          res,
          "New password must be different from current password"
        );
      }

      // 4. Hash new password
      const salt = await bcrypt.genSalt(10);
      const newHashedPassword = await bcrypt.hash(newPassword, salt);

      console.log("New hashed password:", newHashedPassword, userId);
      await db.query(AUTH.UPDATE_PASSWORD, [newHashedPassword, userId]);

      return response(res, 200, "Password updated successfully");
    } catch (error) {
      console.error("Error during password update:", error);
      return errorResponse(res, "Failed to update password");
    }
  }
);

export const verifyUserToken = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      let { id } = req.params;

      if (!id || isNaN(Number(id))) {
        return errorResponse(res, "User ID must be a valid number");
      }

      const userId = Number(id);
      if (!userId) {
        return errorResponse(res, "User ID parameter is required");
      }
      const token = req.cookies?.authToken;

      if (!token) {
        return errorResponse(res, "Invalid or expired token");
      }

      let decodedToken;
      try {
        decodedToken = verifyToken(token) as JwtPayload;
      } catch (err) {
        return errorResponse(res, "Invalid or expired token");
      }

      const loginId = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        userId,
      ]);

      const isMatch = decodedToken.id === loginId.rows[0].id;

      if (!isMatch) {
        return errorResponse(res, "Invalid or expired token");
      }
      return response(res, 200, "Token verified successfully");
    } catch (error) {
      console.error("Error during token verification:", error);
      return errorCatchResponse(res, "Internal Server Error");
    }
  }
);
 
export const registerAgentAccount = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      accountType,
      email,
      password,
      confirmPassword,
      referalDiscount, // Assuming this is part of the request body

    }: {
      accountType: keyof typeof AGENT_ACCOUNT_TYPE;
      email: string;
      password: string;
      confirmPassword: string;
      referalDiscount?: string; // Optional field
    } = req.body;

    // 1. Validate input
    const { success, error } = agentRegisterSchema.safeParse({
      accountType,
      email,
      password,
      confirmPassword,
    });

    if (!success) {
      return errorResponse(res, error.issues[0].message);
    }

    // Generate unique username
    const userName = await generateUniqueUsername(email);

    // Start transaction
    const client = await db.connect();
    try {
      await client.query("BEGIN");

      // 2. Check if email already exists
      const result = await client.query(AUTH.SELECT_BY_EMAIL, [email]);
      if (result.rows.length > 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Email already registered.");
      }
      const accountTypeValue = AGENT_ACCOUNT_TYPE[accountType];

      const { query, values } = AUTH.INSERT_INTO_PROFILE({
        email,
        accountType: accountTypeValue,
        isProfileCompleted: false,
      });

     const profileResult = await client.query(query, values); 
      const updateLeadIfExists = await client.query(
        `SELECT * FROM "${TABLE.LEADS}" WHERE "email" = $1`,
        [email]
      );

      if (updateLeadIfExists.rows.length > 0) {
       await client.query(
          `UPDATE "${TABLE.LEADS}" 
          SET "isConverted" = true, "statusId" = 18  AND "leadType" = $2
          WHERE "email" = $1`,
          [email , accountTypeValue]
        );
 
      }  


      if (profileResult.rows.length > 0) {
        const user = profileResult.rows[0];
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(password, salt);

        const otp = generateOTP();
        const expireTime = new Date(Date.now() + 10 * 60 * 1000);

        const loginResult = await client.query(AUTH.INSERT_INTO_LOGIN, [
          user.id,
          userName,
          "email",
          hashedPassword,
          otp,
          expireTime,
        ]);

        let roleName = "";

        if (AGENT_ACCOUNT_TYPE[accountType] === "Individual") {
          roleName = "agent";
        } else {
          roleName = "agency";
        }
       
        const roleData = await client.query(AUTH.SELECT_ROLE_BY_NAME, [roleName]);

        if (loginResult.rows.length > 0 && roleData.rows.length > 0) {
          await client.query(AUTH.INSERT_INTO_LOGIN_ROLE, [
            loginResult?.rows[0]?.id,
            roleData.rows[0].id,
            loginResult?.rows[0]?.id,
          ]);

          // Generate unique referral ID using nanoid
          const referralId = `faa-${nanoid(8)}`;

          // Insert into referrals table
          await client.query(REFERRALS.INSERT_INTO_REFERRALS, [
            user.id, // profileId
            email, // email
            referralId, // referralId
          ]);


    if(referalDiscount){
      try { 
  
          const getSalesPersonsId = await client.query( `SELECT "id" FROM look.salespersons WHERE "referral_id" = $1`,  [referalDiscount] );
          if (getSalesPersonsId.rows.length > 0) {
            const salesPersonId = getSalesPersonsId.rows[0].id;
            await client.query(
            `INSERT INTO "referralRecords" ("userId", "salesPersonId", "code") VALUES ($1, $2, $3)`,
            [user.id, salesPersonId, referalDiscount]
          );
            // await client.query( `INSERT INTO referralRecords ("userId", "salesPersonId", "code") VALUES ($1, $2, $3)`,[user.id,salesPersonId, decryptedData.id]);
          } else {
            console.error("Salesperson not found for the given email");
          }} catch (err) {
          console.error("Failed to decrypt referral", err);
          }
    }
          // --- BEGIN: Invite & Mapping logic ---
          // 1. Check for invite for this email
          const inviteResult = await client.query(
              `SELECT * FROM agency_team_invites WHERE TRIM(LOWER(email)) = TRIM(LOWER($1)) AND status = 'invited' LIMIT 1`,
              [email]
          );
          if (inviteResult.rows.length > 0) {
            const invite = inviteResult.rows[0];
            // 2. Update invite row
            await client.query(
                `UPDATE agency_team_invites SET agent_profile_id = $1, status = 'accepted', responded_at = NOW() WHERE id = $2`,
                [user.id, invite.id]
            );
            // 3. Insert mapping
            await client.query(
                `INSERT INTO agent_agency_mapping (agency_id, agent_id, created_at) VALUES ($1, $2, NOW())`,
                [invite.agency_id, user.id]
            );
          }
          // --- END: Invite & Mapping logic ---

          await sendRegistrationEmail(userName, email, res, otp);
        }
      } else {
        await client.query("ROLLBACK");
        return response(res, 500, "Failed to send OTP");
      }

      // Commit transaction
      await client.query("COMMIT");
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error during registration:", error);
      return errorResponse(res, "Registration failed. Please try again.");
    } finally {
      client.release();
    }
  }
);

export const fetchUserAccountType = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.params.id ? Number(req.params.id) : req.user?.id;

      if (!userId) {
        return errorResponse(res, "User ID is required");
      }

      const result = await db.query(
        `SELECT * FROM "${TABLE.PROFILE_TABLE}"
        WHERE "id" = $1`,
        [userId]
      );

      if (result.rows.length === 0) {
        return errorResponse(res, "No account found for this user");
      }
      const accountType = result.rows[0].accountType;

      if (result.rows[0].isProfileCompleted === true) {
        return responseData(res, 200, "Account type fetched successfully", {
          accountType,
        });
      }
      return responseData(res, 200, "Account type fetched successfully", {
        accountType,
      });
    } catch (error) {
      console.error("Error fetching account type:", error);
      return errorResponse(res, "Failed to fetch account type");
    }
  }
);

export const fetchUserAccountTypeForResubmit = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.params.id ? Number(req.params.id) : req.user?.id;

      if (!userId) {
        return errorResponse(res, "User ID is required");
      }

      const result = await db.query(
        `SELECT * FROM "${TABLE.PROFILE_TABLE}"
        WHERE "id" = $1`,
        [userId]
      );

      if (result.rows.length === 0) {
        return errorResponse(res, "No account found for this user");
      }

      if (Number(result.rows[0].statusId) === 3) {
        return response(res, 202, "Your Profile is already in review.");
      }

      const resultStatus = await db.query(
        `SELECT * FROM "${TABLE.STATUSES}" WHERE "id" = $1`,
        [Number(result.rows[0].statusId)]
      );
      const status = resultStatus.rows[0].name;
      const accountType = result.rows[0].accountType;
      return responseData(res, 200, "Account type fetched successfully", {
        accountType,
        status,
      });
    } catch (error) {
      console.error("Error fetching account type:", error);
      return errorResponse(res, "Failed to fetch account type");
    }
  }
);
