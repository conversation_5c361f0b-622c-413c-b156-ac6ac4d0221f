import { LanguageDTO, LanguageSchema } from "../dto/language/LanguageDTO";
import { LanguageRepository } from "../repo/LanguageRepository";

export class LanguageService {

    private languageRepository = new LanguageRepository();

    async getAllLanguages(): Promise<LanguageDTO[]> {
        const languages = await this.languageRepository.findAll();
        return languages as LanguageDTO[];
    }
    
}