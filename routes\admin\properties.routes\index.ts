import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import {
  createNote,
  deleteProperty,
  getAllProperties,
  getNotesOfProperty,
  getPropertyById,
  togglePropertyFlag,
  updatePropertyStatus,
} from "../../../controller/admin/properties.controller";

const router = Router();
const upload = storageData("documents");

// GET all properties with pagination and filters
router.get("/", getAllProperties);

// GET property by ID
router.get("/:id", getPropertyById);

// PUT update property status
router.put("/:id/status", upload.none(), updatePropertyStatus);

// PUT toggle featured/verified flags
router.put("/:id/flag", upload.none(), togglePropertyFlag);

// DELETE property
router.delete("/:id", deleteProperty);

// create a note for a property
router.post("/:id/notes", upload.none(), createNote);

// GET notes of a property
router.get("/note/:id", getNotesOfProperty);

export default router;
