import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";
import { AgentServicesService } from "../../../service/services/AgentServicesService";

export class ServiceController {
  private agentServicesService = new AgentServicesService();

  getAllServices = asyncHandler(async (req: Request, res: Response) => {
    try {
      const queryFilter = {
        ...req.query,
        agentId: req.user?.id,
        isDeleted: false
      };
      const result = await this.agentServicesService.getAllServices(
        queryFilter
      );
      return responseData(res, 200, "Services fetched successfully", result);
    } catch (err: any) {
      console.error("Get All Services Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to fetch services."
      );
    }
  });

  // READ single
  getServiceById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.agentServicesService.getServiceById(
        Number(req.params.id)
      );
      return responseData(res, 200, "Service fetched successfully", result);
    } catch (err: any) {
      console.error("Get Service Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to fetch service."
      );
    }
  });

  // CREATE or UPDATE
  createOrUpdateService = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.agentServicesService.createOrUpdateService(req);
      return responseData(res, 200, "Service saved successfully", result);
    } catch (err: any) {
      console.error("Service Save Error:", err);
      let message: any;
      try {
        message = JSON.parse(err.message);
      } catch {
        message = err.message;
      }
      return response(
        res,
        err.statusCode || 500,
        message || "An unexpected error occurred while saving service."
      );
    }
  });

  // PATCH statusId
  updateStatus = asyncHandler(async (req: Request, res: Response) => {
    try {
      // First, get the service to check its current status
      const service = await this.agentServicesService.getServiceById(
        Number(req.params.id)
      );

      // Check if service is blocked by admin by checking status name from database
      const serviceStatus = await this.agentServicesService.getStatusById(service.statusId);
      
      if (serviceStatus && serviceStatus.name?.toLowerCase() === 'blocked') {
        return response(
          res,
          403,
          "This service has been blocked by admin and cannot be modified."
        );
      }

      await this.agentServicesService.updateStatus(
        Number(req.params.id),
        req.body.status
      );
      return response(res, 200, "Status updated successfully");
    } catch (err: any) {
      console.error("Update Status Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to update status."
      );
    }
  });

  // DELETE
  deleteService = asyncHandler(async (req: Request, res: Response) => {
    try {
      await this.agentServicesService.deleteService(Number(req.params.id));
      return response(res, 200, "Service deleted successfully.");
    } catch (err: any) {
      console.error("Delete Service Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to delete service."
      );
    }
  });

  // ADD/REMOVE photos
  updatePhotos = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.agentServicesService.updatePhotos(req);
      return responseData(
        res,
        200,
        "Service photos updated successfully.",
        result
      );
    } catch (err: any) {
      console.error("Update Photos Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to update photos."
      );
    }
  });

  getUserServicesOrMissionsAndTypes = asyncHandler(
    async (req: Request, res: Response) => {
      try {
        const services =
          await this.agentServicesService.getUserMissionsAndTypes(
            Number(req.user?.id)
          );
        return responseData(
          res,
          200,
          "Services fetched successfully.",
          services
        );
      } catch (err: any) {
        console.error("Fetching Services Error:", err);
        return response(
          res,
          err.statusCode || 500,
          err.message || "Failed to fetch services."
        );
      }
    }
  );
}
