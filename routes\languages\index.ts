import express from "express";
import { LanguageController } from "../../controller/language/LanguageController";
import { requestValidatorMiddleware } from "../../middleware/requestValidatorMiddleware";

const router = express.Router();

const languageController: LanguageController = new LanguageController();

/**
 * @swagger
 * /languages:
 *   get:
 *     summary: Get all languages
 *     tags: [Languages]
 *     responses:
 *       200:
 *         description: List of languages
 */
router.get("/", languageController.getAllLanguages);

export default router;