import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express"; 

export const sendEmailToReferalDetails = async (
  firstName: string, 
  email: string,  
  csv   : any,
  title: string
): Promise<void> => {
  try { 
    const mailToApplicant: nodemailer.SendMailOptions = {
      from: process.env.VERIFICATION_EMAIL as string,
      to: email,
      subject: "",
      html: await ReferalTemplate(firstName ,title), 
      attachments: [
        {
          filename: `${firstName}'s ${title}.csv`,
          content: csv,
        },
      ],
    };
 
    await Promise.all([
      transporter.sendMail(mailToApplicant),
    
    ]);
    console.log("Email send to Lead successfully");
  } catch (error) {
    console.error("Error sending to Lead emails:", error);
  }
}; 
 

const ReferalTemplate = async (firstName: string,title: string) => `
<html lang="en">
  <head><meta charset="UTF-8" /></head>
  <body style="font-family:Arial,sans-serif;color:#333;background:#f9f9f9">
    <div style="max-width:600px;margin:40px auto;background:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1)">
      <h2 style="color:#5e9b6d;text-align:center">${title} Program FindAnyAgent!</h2>
      <p>Dear ${firstName},</p>

      <p style="font-size: 16px;">Please find attached your ${title} report in CSV format.</p>
      <br/>
      <br/>
      <br/>
      <p style="font-size: 16px;">In the meantime, feel free to follow us for updates:<br/>
        <a href="${process.env.INSTAGRAM_URL}" style="color:#004aad;">@findanyagent on Instagram</a>
      </p>
      
      <p style="margin-top:30px">Thanks again for joining the future of agent success in the UAE.</p>
      <p style="margin-top:10px">Best regards,<br /><strong>FindAnyAgent Team</strong></p>
      <p style="margin-top:10px"><a href="${process.env.WEBSITE_URL}" style="color: #004aad;">www.findanyagent.ae</a></p>
    </div>
  </body>
</html>
`;

 
 