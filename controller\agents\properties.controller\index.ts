import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import { TABLE } from "../../../utils/database/table";
import { generateUniqueSlug } from "../../../utils/helperFunctions/generateUniqueSlug";
import {
  deleteFileFromS3,
  uploadFileToS3,
} from "../../../utils/services/s3-bucket";
import { AUTH } from "../../../utils/database/queries/auth";
import { sanitizeAndValidate } from "../../../utils/validations/property.validation";

// -------------------- GET ALL PROPERTIES --------------------
export const getAllProperties = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const {
        page = 1,
        pageSize = 10,
        status,
        propertyTypeId,
        locationId,
        listingType,
      } = req.query;

      const limit = parseInt(String(pageSize), 10) || 10;
      const currentPage = parseInt(String(page), 10) || 1;

      let statusId: number | null = null;

      if (status && String(status).trim().toLowerCase() !== "all") {
        const statusName = String(status).trim();
        const { rows: statusRow } = await db.query(
          `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
          [statusName]
        );
        if (statusRow.length === 0)
          return error(res, 400, `Status '${statusName}' not found.`);
        statusId = statusRow[0].id;
      }

      const params = [
        statusId,
        propertyTypeId ? Number(propertyTypeId) : null,
        locationId ? Number(locationId) : null,
        listingType ? Number(listingType) : null,
        limit,
        currentPage,
      ];

      const { rows } = await db.query(
        `SELECT * FROM get_filtered_properties($1, $2, $3, $4, $5, $6)`,
        params
      );

      const statusCountParams = [
        propertyTypeId ? Number(propertyTypeId) : null,
        locationId ? Number(locationId) : null,
        listingType ? Number(listingType) : null,
      ];

      const { rows: statusCounts } = await db.query(
        `SELECT * FROM get_property_status_counts($1, $2, $3)`,
        statusCountParams
      );

      if (!rows.length) {
        return responseData(res, 200, "No properties found", {
          properties: [],
          pagination: {
            total: 0,
            totalPages: 0,
            currentPage,
            perPage: limit,
          },
          statusCounts,
        });
      }

      const { total_count, total_pages } = rows[0];
      const properties = rows.map(
        ({ total_count, total_pages, ...rest }) => rest
      );

      return responseData(res, 200, "Properties fetched successfully", {
        properties,
        pagination: {
          total: total_count,
          totalPages: total_pages,
          currentPage,
          perPage: limit,
        },
        statusCounts,
      });
    } catch (error) {
      console.error(error);
      return response(res, 500, "Something went wrong. Please try again later");
    }
  }
);

// -------------------- GET PROPERTY BY ID --------------------
export const getPropertyById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const id = Number(req.params.id);
      if (!id) return error(res, 400, "Property ID is required");

      const { rows } = await db.query("SELECT * FROM get_property_detail($1)", [
        id,
      ]);

      if (rows.length === 0) return error(res, 404, "Property not found");

      return responseData(res, 200, "Property fetched successfully", rows[0]);
    } catch (error) {
      console.log(error);
      return response(
        res,
        500,
        "Something wents wrong. Please try again later"
      );
    }
  }
);

// -------------------- CREATE OR UPDATE PROPERTY --------------------
export const createOrUpdateProperty = asyncHandler(
  async (req: Request, res: Response) => {
    let sanitized: any;
    try {
      sanitized = await sanitizeAndValidate(req.body, {
        nonNullableKeys: [
          "code",
          "name",
          "locationId",
          "price",
          "size",
          "listingType",
          "createdBy",
        ],
        booleanKeys: [
          "parking",
          "swimmingPools",
          "gym",
          "isFeatured",
          "isVerified",
          "furnished",
        ],
      });
    } catch (err: any) {
      if (err.name === "ValidationError") {
        return res.status(422).json({ error: err.message });
      }
      throw err; // Let asyncHandler or Express middleware catch other errors
    }

    const {
      id,
      code,
      name,
      local,
      propertyTypeId,
      apartmentTypeId,
      totalRooms,
      locationId,
      address,
      currencyId,
      price,
      size,
      permitNo,
      parking,
      swimmingPools,
      gym,
      startDate,
      isFeatured,
      isVerified,
      adminNote,
      expiryDate,
      listingType,
      completionStatus,
      ownershipTypeId,
      metaTitle,
      metaDescription,
      bedrooms,
      bathrooms,
      furnished,
      permitId,
      unitNo,
      projectId,
      tagLine,
    } = sanitized;

    const statusName = String("Pending").trim();
    const { rows: statusRow } = await db.query(
      `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
      [statusName]
    );
    if (statusRow.length === 0)
      return error(res, 400, `Status '${statusName}' not found.`);
    const statusId = statusRow[0].id;

    const profileId = req.user?.id;

    const { rows: agencyRow } = await db.query(
      `SELECT * FROM agn.agencies WHERE "profileId" = $1 LIMIT 1`,
      [profileId]
    );

    if (agencyRow.length === 0) {
      return res
        .status(404)
        .json({ error: "Agency not found for this profile." });
    }

    const agencyId = agencyRow[0].id;

    // Determine userType
    const { rows: profileRows } = await db.query(AUTH.SELECT_BY_ID, [
      profileId,
    ]);

    const accountType = profileRows[0]?.accountType;
    let userType = "user";
    if (accountType === "Individual") userType = "agent";
    else if (accountType === "Company/Agency/PropertyDeveloper")
      userType = "agency";

    const fileGroups = req.files as {
      [fieldname: string]: Express.Multer.File[];
    };

    const govtQrFile = fileGroups["govtIssuedQr"]?.[0] || null;
    const photoFiles = fileGroups["propertyPhotos"] || [];

    let uploadedQrKey: string | null = null;
    let govtIssuedQr: string | null = null;
    let uploadedPhotoKeys: string[] = [];

    try {
      // Begin transaction
      await db.query("BEGIN");

      // Upload govtIssuedQr
      if (govtQrFile) {
        const qrResult = await uploadFileToS3(
          govtQrFile.filename,
          govtQrFile,
          userType,
          "property-qr"
        );
        uploadedQrKey = qrResult.fileKey;
        govtIssuedQr = qrResult.fileKey;
      }

      // Upload property photos
      if (photoFiles.length > 0) {
        const photoResults = await Promise.all(
          photoFiles.map((file) =>
            uploadFileToS3(file.filename, file, userType, "property-photo")
          )
        );
        uploadedPhotoKeys = photoResults.map((r) => r.fileKey);
      }

      // Generate slug
      const slug = await generateUniqueSlug(name, "agn.properties", "slug", id);

      const createdBy = profileId || 1;

      const params = [
        id ?? null,
        code ?? null,
        name ?? null,
        local ?? null,
        agencyId ?? null,
        propertyTypeId ?? null,
        apartmentTypeId ?? null,
        totalRooms ?? null,
        locationId ?? null,
        address ?? null,
        currencyId ?? null,
        price ?? null,
        size ?? null,
        permitNo ?? null,
        parking ?? null,
        swimmingPools ?? null,
        gym ?? null,
        startDate ?? null,
        statusId ?? null,
        createdBy ?? null,
        isFeatured ?? null,
        isVerified ?? null,
        adminNote ?? null,
        expiryDate ?? null,
        listingType ?? null,
        completionStatus ?? null,
        ownershipTypeId ?? null,
        slug ?? null,
        metaTitle ?? null,
        metaDescription ?? null,
        bedrooms ?? null,
        bathrooms ?? null,
        furnished ?? null,
        permitId ?? null,
        unitNo ?? null,
        govtIssuedQr ?? null,
        projectId ?? null,
        tagLine ?? null,
      ];

      // Call stored procedure
      const { rows } = await db.query(
        `SELECT * FROM create_or_update_property(${params
          .map((_, i) => `$${i + 1}`)
          .join(", ")})`,
        params
      );

      const property = rows[0];

      const statusNames = ["Verified"];

      const approvedStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      const loginRes = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);
      const loginId = loginRes.rows[0].id;

      // Insert images
      if (uploadedPhotoKeys.length > 0) {
        const insertImages = uploadedPhotoKeys.map((key) =>
          db.query(
            `INSERT INTO agn.images ("propertyId", "imageUrl", "statusId", "mediaTypeId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
            [property.id, key, approvedStatus?.rows[0]?.id, 48, loginId]
          )
        );
        await Promise.all(insertImages);
      }

      // ✅ Handle features/amenities
      const rawFeatures = req.body.features;
      let featureList: string[] = [];

      if (typeof rawFeatures === "string") {
        featureList = rawFeatures
          .split(",")
          .map((f) => f.trim())
          .filter(Boolean);
      } else if (Array.isArray(rawFeatures)) {
        featureList = rawFeatures.map((f) => String(f).trim()).filter(Boolean);
      }

      if (featureList.length > 0) {
        const insertFeatures = [];

        for (const feature of featureList) {
          const { rows: existing } = await db.query(
            `SELECT 1 FROM agn.features WHERE "propertyId" = $1 AND LOWER("featureName") = LOWER($2) LIMIT 1`,
            [property.id, feature]
          );

          if (existing.length === 0) {
            insertFeatures.push(
              db.query(
                `INSERT INTO agn.features ("propertyId", "featureName", "statusId", "createdBy", "createdOn")
                 VALUES ($1, $2, $3, $4, NOW())`,
                [property.id, feature, approvedStatus?.rows[0]?.id, createdBy]
              )
            );
          }
        }

        if (insertFeatures.length > 0) {
          await Promise.all(insertFeatures);
        }
      }

      // Commit transaction
      await db.query("COMMIT");

      return responseData(res, 200, "Property saved successfully", property);
    } catch (err: any) {
      // Rollback DB transaction
      await db.query("ROLLBACK");

      console.log(uploadedQrKey, uploadedPhotoKeys);

      // Cleanup uploaded S3 files
      const allKeysToDelete = [uploadedQrKey, ...uploadedPhotoKeys].filter(
        Boolean
      );
      await Promise.all(allKeysToDelete.map((key) => deleteFileFromS3(key!)));

      console.log(err);
      return res.status(500).json({
        message: "Failed to create/update property",
        error: err.message,
      });
    }
  }
);

// -------------------- UPDATE STATUS --------------------
export const updatePropertyStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { status } = req.body;

    if (!id || !status) {
      return error(res, 400, "Property ID and status are required");
    }

    const statusName = String(status).trim();

    // Lookup statusId from name
    const { rows: statusRow } = await db.query(
      `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
      [statusName]
    );

    if (statusRow.length === 0) {
      return error(res, 400, `Status '${statusName}' not found.`);
    }

    const statusId = statusRow[0].id;

    // Direct update query
    const result = await db.query(
      `UPDATE agn.properties SET "statusId" = $1, "modifiedOn" = NOW() WHERE id = $2`,
      [statusId, id]
    );

    if (result.rowCount === 0) {
      return error(res, 404, "Property not found or update failed");
    }

    return response(res, 200, "Status updated successfully");
  }
);

// -------------------- TOGGLE PROPERTY FLAG --------------------
export const togglePropertyFlag = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { column } = req.body;

    if (!id || !column)
      return error(res, 400, "Property ID and column name are required");

    const allowedColumns = ["isFeatured", "isVerified"];
    if (!allowedColumns.includes(column))
      return error(res, 400, "Invalid column name");

    // Step 1: Get current value
    const result = await db.query(
      `SELECT "${column}" FROM agn.properties WHERE id = $1`,
      [id]
    );

    if (result.rowCount === 0) {
      return error(res, 404, "Property not found");
    }

    const currentValue = result.rows[0][column];
    const newValue = !currentValue;

    // Step 2: Update with toggled value
    const updateResult = await db.query(
      `
        UPDATE agn.properties
        SET "${column}" = $1, "modifiedOn" = NOW()
        WHERE id = $2
      `,
      [newValue, id]
    );

    return response(
      res,
      200,
      `The property’s '${
        column === "isFeatured" ? "Featured" : "Verified"
      }' status has been successfully toggled to '${
        newValue ? "Enabled" : "Disabled"
      }'.`
    );
  }
);

// -------------------- DELETE PROPERTY --------------------
export const deleteProperty = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    if (!id) return error(res, 400, "Property ID is required");

    // Ensure the property exists before deletion
    const { rowCount } = await db.query(
      `DELETE FROM agn.properties WHERE id = $1`,
      [id]
    );

    if (rowCount === 0) {
      return error(res, 404, "Property not found or already deleted");
    }

    return response(res, 200, "Property deleted successfully.");
  }
);

// -------------------- UPLOAD PROPERTY PHOTOS --------------------
export const updatePropertyPhotos = asyncHandler(
  async (req: Request, res: Response) => {
    const { propertyId } = req.params;
    const { photoIdsToDelete } = req.body; // Array of photo IDs to delete

    if (!propertyId) {
      return res.status(400).json({ error: "Property ID is required." });
    }

    const fileGroups = req.files as {
      [fieldname: string]: Express.Multer.File[];
    };
    const photoFiles = fileGroups["propertyPhotos"] || [];

    let uploadedPhotoKeys: string[] = [];

    try {
      await db.query("BEGIN");

      // 1. Delete existing photos if `photoIdsToDelete` is provided
      if (photoIdsToDelete) {
        let idsToDelete: number[] = [];

        if (typeof photoIdsToDelete === "string") {
          idsToDelete = photoIdsToDelete
            .split(",")
            .map((id) => parseInt(id.trim(), 10))
            .filter((id) => !isNaN(id));
        } else if (Array.isArray(photoIdsToDelete)) {
          idsToDelete = photoIdsToDelete
            .map((id) => parseInt(id, 10))
            .filter((id) => !isNaN(id));
        }

        for (const photoId of idsToDelete) {
          const { rows } = await db.query(
            `SELECT "imageUrl" FROM agn.images WHERE id = $1 AND "propertyId" = $2`,
            [photoId, propertyId]
          );

          if (rows.length > 0) {
            const fileKey = rows[0].imageurl;

            await deleteFileFromS3(fileKey);
            await db.query(`DELETE FROM agn.images WHERE id = $1`, [photoId]);
          }
        }
      }

      // 2. Upload new photos if provided
      if (photoFiles.length > 0) {
        const profileId = req.user?.id;

        // Determine userType
        const { rows: profileRows } = await db.query(AUTH.SELECT_BY_ID, [
          profileId,
        ]);

        const accountType = profileRows[0]?.accountType;
        let userType = "user";
        if (accountType === "Individual") userType = "agent";
        else if (accountType === "Company/Agency/PropertyDeveloper")
          userType = "agency";

        const photoResults = await Promise.all(
          photoFiles.map((file) =>
            uploadFileToS3(file.filename, file, userType, "property-photo")
          )
        );
        uploadedPhotoKeys = photoResults.map((r) => r.fileKey);

        // Get approved status
        const statusNames = ["Verified"];
        const approvedStatus = await db.query(
          AUTH.SELECT_ACCOUNT_STATUS(statusNames),
          statusNames
        );

        const loginRes = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
          profileId,
        ]);
        const loginId = loginRes.rows[0].id;

        // Insert new images into DB
        const insertImages = uploadedPhotoKeys.map((key) =>
          db.query(
            `INSERT INTO agn.images ("propertyId", "imageUrl", "statusId", "mediaTypeId", "createdBy")
           VALUES ($1, $2, $3, $4, $5)`,
            [propertyId, key, approvedStatus?.rows[0]?.id, 48, loginId]
          )
        );
        await Promise.all(insertImages);
      }

      await db.query("COMMIT");

      const { rows } = await db.query("SELECT * FROM get_property_detail($1)", [
        propertyId,
      ]);

      return responseData(
        res,
        200,
        "Property photos updated successfully.",
        rows[0]
      );
    } catch (err: any) {
      await db.query("ROLLBACK");

      // Cleanup uploaded S3 files if failure
      if (uploadedPhotoKeys.length > 0) {
        await Promise.all(
          uploadedPhotoKeys.map((key) => deleteFileFromS3(key))
        );
      }

      console.error(err);
      return res.status(500).json({
        message: "Failed to update property photos",
        error: err.message,
      });
    }
  }
);
