import { Request, Response } from "express";
import async<PERSON>and<PERSON> from "../../../middleware/trycatch";
import { error, response, responseData } from "../../../utils/response";
import { TABLE } from "../../../utils/database/table";
import { <PERSON>rser } from "json2csv"; // npm install json2csv
import { sendEmailToReferalDetails } from "../../../utils/services/nodemailer/referalDetails";
import { db } from "../../../config/database";
export const getAllReferals = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      search = "",
      page = 1,
      pageSize = 10,
      status,
      plan,
      monthAndYear,
    } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const limit = Number(pageSize);

    let conditions: string[] = [];
    let values: any[] = [];
    let idx = 1;

    // Search filter
    if (search) {
      conditions.push(`
        (
          sp.full_name ILIKE $${idx} OR
          sp.email ILIKE $${idx} OR
          sp.referral_id ILIKE $${idx} OR
          u."firstName" ILIKE $${idx} OR
          u."middleName" ILIKE $${idx} OR
          u."lastName" ILIKE $${idx} OR
          u.email ILIKE $${idx} OR
          u.phone ILIKE $${idx}
        )
      `);
      values.push(`%${search}%`);
      idx++;
    }

    // Status filter (commission status)
    if (status) {
      conditions.push(`st."name" ILIKE $${idx}`);
      values.push(status);
      idx++;
    }

    // Plan filter
    if (plan) {
      conditions.push(`s."package" ILIKE $${idx}`);
      values.push(plan);
      idx++;
    }

    // Month/year filter
    if (monthAndYear) {
      conditions.push(`TO_CHAR(r."created_at", 'Month YYYY') ILIKE $${idx}`);
      values.push(monthAndYear);
      idx++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

    // Main query
    const mainQuery = `
      SELECT 
        r.*, 
        r."created_at" AS referred_date,
        CASE WHEN r.id IS NULL THEN false ELSE true END AS "hasReferral",

        c.id AS commission_id,
        c."subscriptionId",
        c."commissionAmount",
        c."paidAt",
        c."status" AS commission_status_id,
        st."name" AS commission_status_name,
        c."adminApproval",
        c."referralId",

        sp.id AS salesperson_id,
        sp.full_name AS salesperson_name,
        sp.email AS salesperson_email,
        sp.phone AS salesperson_phone,
        sp.referral_id AS salesperson_referral_id,
        sp.commission_rate AS salesperson_commission_rate,

        u."firstName" AS user_firstname,
        u."middleName" AS user_middlename,
        u."lastName" AS user_lastname,
        u.email AS user_email,
        u.phone AS user_phone,

        s.id AS subscription_id,
        s."package" AS subscription_plan_name,
        s."price" AS subscription_price,
        s."paymentStatusId" AS subscription_paymentStatusId,
        s."startDate" AS subscription_startDate

      FROM "referralRecords" r
      LEFT JOIN "commission" c ON c."referralId" = r.id
      LEFT JOIN look.status st ON st.id = c."status"
      LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
      LEFT JOIN "${TABLE.PROFILE_TABLE}" u ON u.id = r."userId"
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      ${whereClause}
      ORDER BY r."created_at" DESC
      LIMIT $${idx} OFFSET $${idx + 1};
    `;

    values.push(limit, offset);

    // Count query
    const countQuery = `
      SELECT COUNT(*) 
      FROM "referralRecords" r
      LEFT JOIN "commission" c ON c."referralId" = r.id
      LEFT JOIN look.status st ON st.id = c."status"
      LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
      LEFT JOIN "${TABLE.PROFILE_TABLE}" u ON u.id = r."userId"
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      ${whereClause};
    `;

    // Month/year dropdown values (include all referrals)
    const monthYearQuery = `
      SELECT 
        TO_CHAR(r."created_at", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', r."created_at") AS month_date
      FROM "referralRecords" r
      GROUP BY month_year, month_date
      ORDER BY month_date DESC;
    `;

    // Available plans (include all referrals)
    const availablePlansQuery = `
      SELECT DISTINCT s."package" AS plan_name
      FROM "referralRecords" r
      LEFT JOIN "commission" c ON c."referralId" = r.id
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      WHERE s."package" IS NOT NULL
      ORDER BY s."package" ASC;
    `;

    const [dataResult, countResult, monthYearResult, availablePlansResult] =
      await Promise.all([
        db.query(mainQuery, values),
        db.query(countQuery, values.slice(0, idx - 1)),
        db.query(monthYearQuery),
        db.query(availablePlansQuery),
      ]);

    const total = Number(countResult.rows[0].count);
    const returned = dataResult.rows.length;

    return res.status(200).json({
      status: 200,
      success: true,
      message: "Referrals fetched successfully",
      data: dataResult.rows,
      pagination: {
        page: Number(page),
        total,
        pageSize: limit,
        returned,
      },
      availableMonths: monthYearResult.rows.map((row: any) => row.month_year.trim()),
      availablePlans: availablePlansResult.rows.map((row: any) => row.plan_name),
    });
  }
);

export const updateReferal = asyncHandler(
  async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { status } = req.body;

    if (!id) return error(res, 400, "Referral ID is required.");
    const statusS =
      status === "Cancelled"
        ? 12
        : status === "Paid"
        ? 13
        : status === "Confirmed"
        ? 30
        : 3;
    const client = await db.connect();
    try {
      await client.query("BEGIN");
      // Update the referral status
      if (statusS === 13) {
        const { rows: result } = await client.query(
          `UPDATE commission SET status = $1 , "paidAt" = NOW() WHERE id = $2 RETURNING *`,
          [statusS, id]
        );
        await client.query("COMMIT");
        return responseData(res, 200, "Referral status updated.", result);
      } else {
        const { rows: result } = await client.query(
          `UPDATE commission SET status = $1, "paidAt" = NULL WHERE id = $2 RETURNING *`,
          [statusS, id]
        );
        await client.query("COMMIT");
        return responseData(res, 200, "Referral status updated.", result);
      }
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Update referral status failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

export const getRecentReferrals = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { limit = 5 } = req.query;

      const { rows } = await db.query(
        `
        SELECT 
          r.id AS referral_id,
          r."created_at" AS referred_date,

          sp.id AS salesperson_id,
          sp.full_name AS salesperson_name,
          sp.email AS salesperson_email,
          sp.phone AS salesperson_phone,
          sp.referral_id AS salesperson_referral_code,

          u.id AS user_id,
          u."firstName" AS user_firstname,
          u."lastName" AS user_lastname,
          u.email AS user_email,
          u.phone AS user_phone,

          c.id AS commission_id,
          c."commissionAmount",
          c."status" AS commission_status_id,
          st."name" AS commission_status_name,
          c."paidAt"

        FROM "referralRecords" r
        INNER JOIN "commission" c ON c."referralId" = r.id
        LEFT JOIN look.status st ON st.id = c."status"
        LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
        LEFT JOIN ${TABLE.PROFILE_TABLE} u ON u.id = r."userId"
        WHERE c."status" IN (13, 30, 3)
        ORDER BY r."created_at" DESC
        LIMIT $1
        `,
        [Number(limit)]
      );

      return responseData(
        res,
        200,
        "Recent referrals fetched successfully",
        rows
      );
    } catch (err) {
      return error(res, 500, "Failed to fetch recent referrals");
    }
  }
);

export const getTopSalespersonsByCommission = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { rows } = await db.query(
        `
        SELECT 
          sp.id AS salesperson_id,
          sp.full_name AS salesperson_name,
          sp.referral_id AS salesperson_referral_code,
          COUNT(r.id) AS referral_count,
          COALESCE(SUM(c."commissionAmount"), 0) AS total_commission

        FROM look.salespersons sp
        INNER JOIN "referralRecords" r ON r."salesPersonId" = sp.id
        INNER JOIN "commission" c ON c."referralId" = r.id
        WHERE c."status" IN (13, 30, 3)

        GROUP BY sp.id, sp.full_name, sp.referral_id
        ORDER BY total_commission DESC
        LIMIT 2;
        `
      );

      return responseData(
        res,
        200,
        "Top 2 salespersons by commission fetched successfully",
        rows
      );
    } catch (err) {
      console.error(err);
      return error(res, 500, "Failed to fetch top performers");
    }
  }
);

export const getSalespersonDashboardStats = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const [
        totalSalespersonsResult,
        activeSalespersonsResult,
        allReferralsResult,
        confirmedReferralsResult,
        totalCommissionResult,
        paidCommissionResult,
      ] = await Promise.all([
        // Total salespersons
        db.query(`SELECT COUNT(*) AS count FROM look.salespersons`),

        // Active salespersons (statusId = 20)
        db.query(
          `SELECT COUNT(*) AS count FROM look.salespersons WHERE "statusId" = 20`
        ),

        // All referrals (total referrals in commission table)
        db.query(`SELECT COUNT(*) AS count FROM "referralRecords"`),

        // Confirmed referrals (status = 30)
        db.query(`SELECT COUNT(*) AS count FROM "commission" `),

        // Total commission amount for statuses 3, 13, 30
        db.query(`
          SELECT COALESCE(SUM("commissionAmount"), 0) AS amount
          FROM "commission"
          WHERE status IN (3, 13, 30)
        `),

        // Paid commission amount (status = 13)
        db.query(`
          SELECT COALESCE(SUM("commissionAmount"), 0) AS amount
          FROM "commission"
          WHERE status = 13
        `),
      ]);

      // Extract values
      const totalSalespersons = Number(totalSalespersonsResult.rows[0].count);
      const activeSalespersons = Number(activeSalespersonsResult.rows[0].count);
      const totalReferrals = Number(allReferralsResult.rows[0].count);
      const confirmedReferrals = Number(confirmedReferralsResult.rows[0].count);
      const totalCommissionAmount = Number(
        totalCommissionResult.rows[0].amount
      );
      const paidCommissionAmount = Number(paidCommissionResult.rows[0].amount);

      // Calculate conversion rate
      const conversionRate =
        totalReferrals > 0
          ? ((confirmedReferrals / totalReferrals) * 100).toFixed(2)
          : "0.00";

      // Calculate average commission per confirmed referral
      const avgCommissionPerReferral =
        confirmedReferrals > 0
          ? (totalCommissionAmount / confirmedReferrals).toFixed(2)
          : "0.00";

      // Send response
      return res.status(200).json({
        status: 200,
        success: true,
        message: "Dashboard stats fetched successfully",
        data: {
          totalSalespersons,
          activeSalespersons,
          totalReferrals,
          confirmedReferrals,
          totalCommissionAmount,
          paidCommissionAmount,
          conversionRate: `${conversionRate}%`,
          avgCommissionPerReferral,
        },
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({
        status: 500,
        success: false,
        message: "Failed to fetch dashboard stats",
      });
    }
  }
);

export const getAllPayments = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      search = "",
      page = 1,
      pageSize = 10,
      monthAndYear,
      paymentMethod,
    } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const limit = Number(pageSize);

    let conditions: string[] = ["c.status = 13", 'c."paidAt" IS NOT NULL'];
    let values: any[] = [];
    let idx = 1;

    if (search) {
      conditions.push(`(
        sp.full_name ILIKE $${idx} OR
        sp.email ILIKE $${idx}
      )`);
      values.push(`%${search}%`);
      idx++;
    }

    if (monthAndYear) {
      conditions.push(`TO_CHAR(c."paidAt", 'Month YYYY') ILIKE $${idx}`);
      values.push(monthAndYear);
      idx++;
    }

    if (paymentMethod) {
      conditions.push(`c."paymentMethod" ILIKE $${idx}`);
      values.push(`%${paymentMethod}%`);
      idx++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

    const limitIdx = idx;
    const offsetIdx = idx + 1;
    values.push(limit, offset);

    const mainQuery = `
        SELECT 
        CONCAT(sp.id, '-', TO_CHAR(DATE_TRUNC('month', c."paidAt"), 'YYYY-MM')) AS unique_id,
        sp.id AS salesperson_id,
        sp.full_name AS salesperson_name,
        sp.email AS salesperson_email,
        TO_CHAR(c."paidAt", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', c."paidAt") AS month_date,
        SUM(c."commissionAmount") AS total_commission,
        MAX(c."paymentMethod") AS payment_method,
        MAX(c."paymentAt") AS payment_at
      FROM "commission" c
      LEFT JOIN look.salespersons sp ON sp.id = c."salesPersonId"
      ${whereClause}
      GROUP BY 
        sp.id, sp.full_name, sp.email,
        TO_CHAR(c."paidAt", 'Month YYYY'), 
        DATE_TRUNC('month', c."paidAt")
      ORDER BY month_date DESC
      LIMIT $${limitIdx} OFFSET $${offsetIdx};
    `;

    const countQuery = `
      SELECT COUNT(*) FROM (
        SELECT 1
        FROM "commission" c
        LEFT JOIN look.salespersons sp ON sp.id = c."salesPersonId"
        ${whereClause}
        GROUP BY 
          sp.id, 
          TO_CHAR(c."paidAt", 'Month YYYY'), 
          DATE_TRUNC('month', c."paidAt")
      ) AS grouped;
    `;

    const monthYearQuery = `
      SELECT 
        TO_CHAR(c."paidAt", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', c."paidAt") AS month_date
      FROM "commission" c
      WHERE c.status = 13 AND c."paidAt" IS NOT NULL
      GROUP BY month_year, month_date
      ORDER BY month_date DESC;
    `;

    const [dataResult, countResult, monthYearResult] = await Promise.all([
      db.query(mainQuery, values),
      db.query(countQuery, values.slice(0, idx - 1)),
      db.query(monthYearQuery),
    ]);

    const total = Number(countResult.rows[0].count);
    const returned = dataResult.rows.length;

    res.status(200).json({
      status: 200,
      success: true,
      message: "Monthly commissions fetched successfully",
      data: dataResult.rows,
      pagination: {
        page: Number(page),
        total,
        pageSize: limit,
        returned,
      },
      availableMonths: monthYearResult.rows
        .map((row: any) => row.month_year)
        .filter((month: any) => month !== null)
        .map((month: any) => month.trim()),
    });
  }
);

export const updatePaymentPaid = asyncHandler(
  async (req: Request, res: Response) => {
    const salespersonId = Number(req.params.id);
    const { monthDate, paymentMethod } = req.body;

    if (!salespersonId || !monthDate || !paymentMethod) {
      return error(
        res,
        400,
        "Salesperson ,Date, and payment Method are required."
      );
    }

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const { rows: updated } = await client.query(
        `
        UPDATE commission
        SET 
          "paymentMethod" = $1,
          "adminApproval" = true,
          "paymentAt" = NOW()
        WHERE 
          "salesPersonId" = $2
          AND DATE_TRUNC('month', "paidAt") = DATE_TRUNC('month', $3::DATE)
          AND status = 13
          AND "paidAt" IS NOT NULL
        RETURNING *;
        `,
        [paymentMethod, salespersonId, monthDate]
      );

      const { rows: created } = await client.query(
        `
        INSERT INTO referal_payments ("salesPersonId", "period", "amount")
        SELECT 
          $1 AS "salesPersonId",
          $2::DATE AS "period",
          COALESCE(SUM("commissionAmount"), 0) AS "amount"
        FROM commission
        WHERE 
          "salesPersonId" = $1
          AND DATE_TRUNC('month', "paidAt") = DATE_TRUNC('month', $2::DATE)
          AND "paymentMethod" = $3
          AND "paymentAt"::DATE = CURRENT_DATE
        RETURNING *;
        `,
        [salespersonId, monthDate, paymentMethod]
      );

      await client.query("COMMIT");

      return responseData(
        res,
        200,
        "Payment updated for the salesperson's month.",
        { updated, created }
      );
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Error updating payment:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

export const getPaymentsBySalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const { salespersonId } = req.params;
    const { page = 1, pageSize = 10, monthAndYear, paymentMethod } = req.query;

    if (!salespersonId) {
      return error(res, 400, "salespersonId is required in params.");
    }

    const offset = (Number(page) - 1) * Number(pageSize);
    const limit = Number(pageSize);

    // ✅ Conditions for mainQuery — only PAID commissions
    let conditions: string[] = [
      "c.status = 13",
      'c."salesPersonId" = $1',
      'c."paymentAt" IS NOT NULL',
    ];
    let values: any[] = [salespersonId];
    let idx = 2;

    if (monthAndYear) {
      conditions.push(`TO_CHAR(c."paymentAt", 'Month YYYY') ILIKE $${idx}`);
      values.push(monthAndYear);
      idx++;
    }

    if (paymentMethod) {
      conditions.push(`c."paymentMethod" ILIKE $${idx}`);
      values.push(`%${paymentMethod}%`);
      idx++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

    const limitIdx = idx;
    const offsetIdx = idx + 1;
    values.push(limit, offset);

    // ✅ Main query: Only paid months grouped
    const mainQuery = `
      SELECT 
        CONCAT(sp.id, '-', TO_CHAR(DATE_TRUNC('month', c."paymentAt"), 'YYYY-MM')) AS unique_id,
        sp.id AS salesperson_id,
        sp.full_name AS salesperson_name,
        sp.email AS salesperson_email,
        TO_CHAR(c."paymentAt", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', c."paymentAt") AS month_date,
        SUM(c."commissionAmount") AS total_commission,
        MAX(c."paymentMethod") AS payment_method,
        MAX(c."paymentAt") AS payment_at,
        rp.id AS referal_payment_id,
        rp.amount AS referal_payment_amount,
        SUM(s."price") AS subscription_price
      FROM "commission" c
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      LEFT JOIN look.salespersons sp 
        ON sp.id = c."salesPersonId"
      LEFT JOIN referal_payments rp 
        ON rp."salesPersonId" = c."salesPersonId"
        AND DATE_TRUNC('month', rp."period"::date) = DATE_TRUNC('month', c."paymentAt")
      ${whereClause}
      GROUP BY 
        sp.id, sp.full_name, sp.email, rp.id, rp.amount,
        TO_CHAR(c."paymentAt", 'Month YYYY'), 
        DATE_TRUNC('month', c."paymentAt")
      ORDER BY month_date DESC
      LIMIT $${limitIdx} OFFSET $${offsetIdx};
    `;

    // ✅ Count query (same conditions as mainQuery, but without LIMIT/OFFSET)
    const countQuery = `
      SELECT COUNT(*) FROM (
        SELECT 1
        FROM "commission" c
        LEFT JOIN look.salespersons sp 
          ON sp.id = c."salesPersonId"
        ${whereClause}
        GROUP BY 
          sp.id, 
          TO_CHAR(c."paymentAt", 'Month YYYY'), 
          DATE_TRUNC('month', c."paymentAt")
      ) AS grouped;
    `;

    // ✅ Available months for filter (only paid commissions)
    const monthYearQuery = `
      SELECT 
        TO_CHAR(c."paymentAt", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', c."paymentAt") AS month_date
      FROM "commission" c
      WHERE c.status = 13 
        AND c."paymentAt" IS NOT NULL
        AND c."salesPersonId" = $1
      GROUP BY month_year, month_date
      ORDER BY month_date DESC;
    `;

    // ✅ Totals query (paid + pending commissions)
    const totalsQuery = `
      SELECT
        COALESCE(SUM(CASE WHEN c."paymentAt" IS NOT NULL THEN c."commissionAmount" ELSE 0 END), 0) AS commission_earned,
        COALESCE(SUM(CASE WHEN c."paymentAt" IS NULL THEN c."commissionAmount" ELSE 0 END), 0) AS pending,
        COUNT(DISTINCT c."referralId") AS total_referrals,
        COALESCE(SUM(s."price"), 0) AS total_value
      FROM "commission" c
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      WHERE c."salesPersonId" = $1
    `;

    // Run all queries
    const [dataResult, countResult, monthYearResult, totalsResult] =
      await Promise.all([
        db.query(mainQuery, values),
        db.query(countQuery, values.slice(0, idx - 1)),
        db.query(monthYearQuery, [salespersonId]),
        db.query(totalsQuery, [salespersonId]),
      ]);

    const total = Number(countResult.rows[0].count);
    const returned = dataResult.rows.length;

    // ✅ Response
    return res.status(200).json({
      status: 200,
      success: true,
      message: "Monthly commissions for salesperson fetched successfully",
      data: dataResult.rows,
      totals: {
        commissionEarned: Number(totalsResult.rows[0].commission_earned),
        pending: Number(totalsResult.rows[0].pending),
        totalReferrals: Number(totalsResult.rows[0].total_referrals),
        totalValue: Number(totalsResult.rows[0].total_value),
      },
      pagination: {
        page: Number(page),
        total,
        pageSize: limit,
        returned,
      },
      availableMonths: monthYearResult.rows
        .map((row: any) => row.month_year)
        .filter((month: any) => month !== null)
        .map((month: any) => month.trim()),
    });
  }
);

export const sendPaymentsBySalesperson = asyncHandler(
  async (req: Request, res: Response) => {
    const { salespersonId } = req.params;
    const { monthAndYear } = req.query;

    if (!salespersonId) {
      return error(res, 400, "salespersonId is required in params.");
    }

    // ✅ Conditions for mainQuery — only PAID commissions
    let conditions: string[] = [
      "c.status = 13",
      'c."salesPersonId" = $1',
      'c."paymentAt" IS NOT NULL',
    ];
    let values: any[] = [salespersonId];
    let idx = 2;

    if (monthAndYear) {
      conditions.push(`TO_CHAR(c."paymentAt", 'Month YYYY') ILIKE $${idx}`);
      values.push(monthAndYear);
      idx++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

    // ✅ Main query — removed LIMIT/OFFSET so all rows are emailed
    const mainQuery = `
      SELECT 
        CONCAT(sp.id, '-', TO_CHAR(DATE_TRUNC('month', c."paymentAt"), 'YYYY-MM')) AS unique_id,
        sp.id AS salesperson_id,
        sp.full_name AS salesperson_name,
        sp.email AS salesperson_email,
        TO_CHAR(c."paymentAt", 'Month YYYY') AS month_year,
        DATE_TRUNC('month', c."paymentAt") AS month_date,
        SUM(c."commissionAmount") AS total_commission,
        MAX(c."paymentMethod") AS payment_method,
        MAX(c."paymentAt") AS payment_at,
        rp.id AS referal_payment_id,
        rp.amount AS referal_payment_amount,
        SUM(s."price") AS subscription_price
      FROM "commission" c
      LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
      LEFT JOIN look.salespersons sp 
        ON sp.id = c."salesPersonId"
      LEFT JOIN referal_payments rp 
        ON rp."salesPersonId" = c."salesPersonId"
        AND DATE_TRUNC('month', rp."period"::date) = DATE_TRUNC('month', c."paymentAt")
      ${whereClause}
      GROUP BY 
        sp.id, sp.full_name, sp.email, rp.id, rp.amount,
        TO_CHAR(c."paymentAt", 'Month YYYY'), 
        DATE_TRUNC('month', c."paymentAt")
      ORDER BY month_date DESC;
    `;

    // ✅ Run query
    const dataResult = await db.query(mainQuery, values);

    // ✅ If there’s data, send email
    if (dataResult.rows.length > 0) {
      const fields = [
        { label: "Salesperson Name", value: "salesperson_name" },
        { label: "Salesperson Email", value: "salesperson_email" },
        { label: "Month Year", value: "month_year" },
        { label: "Total Commission", value: "total_commission" },
        { label: "Payment Method", value: "payment_method" },
        { label: "Payment Date", value: "payment_at" },
        { label: "Referral Payment Amount", value: "referal_payment_amount" },
        { label: "Subscription Price", value: "subscription_price" },
      ];

      const json2csvParser = new Parser({ fields });
      const csv = json2csvParser.parse(dataResult.rows);

      const salespersonEmail = dataResult.rows[0].salesperson_email;
      const salespersonName = dataResult.rows[0].salesperson_name;

      if (salespersonEmail) {
        await sendEmailToReferalDetails(
          salespersonName,
          salespersonEmail,
          csv,
          "Payments"
        );
      }
    }

    return res.status(200).json({
      status: 200,
      success: true,
      message: "Monthly commissions for salesperson emailed successfully",
    });
  }
);
