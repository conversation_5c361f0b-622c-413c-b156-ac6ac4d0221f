import { Router } from "express";
import { storageData } from "../utils/services/multer";
import { getAllStatus, searchAgencies } from "../controller/status";
import { USER_ROUTES } from "../utils/enums/status.enum";
import { createLeadViaSendingEmail } from "../controller/admin/leads";
import {
  getAllPublishedBlogs,
  getBlogDetails,
  getRelatedBlog,
} from "../controller/blogs";
import { createDocumentByAdmin } from "../controller/documents";
import { createNewsLetterSubscriber } from "../controller/admin/blog-subscriber";
import { authMiddleware } from "../middleware/authMiddleware";
import { ServiceController } from "../controller/agents/services/ServiceController";

const servicesController = new ServiceController();

const router: Router = Router();

const upload = storageData("documents");

router.get(USER_ROUTES.STATUS, getAllStatus);

router.get("/search-agency", searchAgencies);

router.post("/lead", upload.none(), createLeadViaSendingEmail);
router.get("/published-blogs", getAllPublishedBlogs);
router.get("/published-blogs/:slug", getBlogDetails);
router.get("/related-blogs/:id", getRelatedBlog);
router.post("/documents", upload.array("document", 10), createDocumentByAdmin);
router.post("/newsletter", upload.none(), createNewsLetterSubscriber);
router.get("/missions-and-types", authMiddleware, servicesController.getUserServicesOrMissionsAndTypes);

export default router;
